# LiveKit Backend

A Python-based backend service for LiveKit that provides voice assistant capabilities using LiveKit Agents.

## Overview

This service integrates LiveKit's real-time communication platform with various AI services to create an intelligent voice assistant. It supports:

- Real-time speech-to-text transcription using Deepgram
- Natural language processing with OpenAI's GPT models
- Text-to-speech synthesis using Cartesia
- Voice activity detection with Silero
- Noise cancellation
- Conversation memory and context management with Mem0
- Conversation history storage in MongoDB

## Features

- **Voice Assistant**: Provides a voice-based AI assistant that can understand and respond to user queries
- **Memory Management**: Maintains conversation context using Mem0 for more coherent interactions
- **Conversation History**: Stores and retrieves conversation history from MongoDB
- **Automatic Summarization**: Generates structured summaries of conversations using a specialized AI summary agent
- **Metrics Collection**: Tracks usage metrics for monitoring and billing

## Assistant Capabilities

The Assistant class extends LiveKit's Agent class and provides:

- **Context-Aware Responses**: Uses Mem0 to search for relevant memories based on user queries
- **Memory Storage**: Automatically stores both user queries and assistant responses
- **Conversation Continuity**: Maintains context across multiple interactions
- **Customizable Instructions**: Supports custom system instructions based on conversation history

## Data Storage

The application uses MongoDB to store conversation data:

- **Conversations**: Stores metadata about each conversation including:
  - User ID
  - Title
  - Channel type
  - AI-generated structured summary
  - Creation and update timestamps

- **Messages**: Stores individual messages with:
  - Conversation ID reference
  - Sender ID
  - Sender type (user or assistant)
  - Message content
  - Timestamps

The system includes a specialized summary generation agent that creates well-structured summaries with:

- Overview of the conversation
- Key topics discussed
- Action items identified
- Questions and follow-ups
- Key decisions and outcomes

## Architecture

The application follows a modular architecture:

1. **Entry Point**: The `main.py` file initializes the LiveKit agent and runs it using the CLI.

2. **Agent Session**: The `session.py` file creates and configures the agent session with:
   - Speech-to-Text (Deepgram)
   - Language Model (OpenAI)
   - Text-to-Speech (Cartesia)
   - Voice Activity Detection (Silero)
   - Turn Detection (Multilingual Model)

3. **Assistant**: The `assistant.py` file extends the LiveKit Agent class to:
   - Process user input
   - Search for relevant memories
   - Generate responses
   - Store conversation history

4. **Database Layer**: The `mongodb.py` file handles:
   - Storing conversations and messages
   - Retrieving conversation history
   - Generating structured conversation summaries using an AI assistant agent
   - Processing transcripts and updating conversation records

5. **Configuration**: The `config.py` file manages environment variables and default settings.

## Prerequisites

- Python 3.10+
- Poetry for dependency management
- MongoDB instance
- API keys for:
  - LiveKit
  - Deepgram
  - OpenAI
  - Cartesia
  - Mem0

## Installation

### Using Poetry

```bash
# Install dependencies
poetry install --no-root

# Run the application
poetry run python main.py dev
```

### Using Docker

```bash
# Build the Docker image
docker build -t livekit-backend .

# Run the Docker container
docker run -p 8000:8000 --env-file .env livekit-backend
```

## Configuration

Copy the `.env.example` file to `.env` and fill in the required environment variables:

```env
LIVEKIT_URL=
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=

DEEPGRAM_API_KEY=

OPENAI_API_KEY=

SUMMARY_GEN_API_KEY=
SUMMARY_GEN_MODEL=

MONGODB_CONN_URI=
MONGODB_DATABASE_NAME=
MONGODB_CONVERSATION_COLLECTION=
MONGODB_MESSAGE_COLLECTION=

KAFKA_BOOTSTRAP_SERVERS=
KAFKA_AGENT_CREATION_TOPIC=
KAFKA_AGENT_CHAT_TOPIC=
KAFKA_AGENT_RESPONSE_TOPIC=
KAFKA_CONSUMER_GROUP=
KAFKA_WORKFLOW_RESPONSES=

API_GATEWAY_BASE_URL=
```

## Project Structure

```plaintext
livekit-backend/
├── app/                      # Application code
│   ├── core/                 # Core functionality
│   │   └── config.py         # Configuration settings
│   ├── db/                   # Database interactions
│   │   └── mongodb.py        # MongoDB client and operations
│   ├── schemas/              # Data models
│   │   ├── conversation.py   # Conversation schema
│   │   └── message.py        # Message schema
│   ├── services/             # Business logic
│   │   ├── conversation.py   # Conversation management
│   │   ├── metrics.py        # Usage metrics collection
│   │   └── session.py        # Agent session management
│   ├── assistant.py          # Assistant implementation
│   └── entrypoint.py         # Application entry point
├── KMS/                      # Kurento Media Server logs
├── Dockerfile                # Docker configuration
├── main.py                   # Main application file
├── pyproject.toml            # Poetry configuration
├── poetry.lock               # Poetry lock file
├── run_local.sh              # Script to run locally
└── .env                      # Environment variables
```

## Usage

### Running Locally

```bash
# Make the script executable
chmod +x run_local.sh

# Run the application
./run_local.sh
```

### Development

The application uses Poetry for dependency management:

```bash
# Add a new dependency
poetry add package-name

# Add a development dependency
poetry add --group dev package-name

# Update dependencies
poetry update
```

## Testing

```bash
# Run tests
poetry run pytest

# Run tests with coverage
poetry run pytest --cov=app
```

## License

[MIT License](LICENSE)
