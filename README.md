# LiveKit Voice Communication Backend

A simplified Python-based backend service for LiveKit that provides voice communication with agent platform integration through Kafka.

## Overview

This service provides a minimal voice communication flow:

1. **Frontend connects to LiveKit room** - Standard LiveKit room connection
2. **Voice processing** - Real-time speech-to-text transcription using Deepgram
3. **Agent platform communication** - Sends user messages to agent platform via Kafka
4. **Response handling** - Receives responses from agent platform and converts to speech using OpenAI TTS
5. **Voice output** - Sends audio response back to frontend through LiveKit

## Features

- **Voice Communication**: Real-time voice input/output through LiveKit
- **Speech-to-Text**: Deepgram integration for transcription
- **Text-to-Speech**: OpenAI TTS for response synthesis
- **Kafka Integration**: Communication with external agent platform
- **Voice Activity Detection**: Silero VAD for better voice processing

## Voice Communication Flow

The simplified voice communication flow:

1. **User speaks** → Deepgram STT converts to text
2. **Text sent to agent platform** → Via Kafka message
3. **Agent platform processes** → Returns response via Kafka
4. **Response converted to speech** → OpenAI TTS synthesis
5. **Audio sent to user** → Through LiveKit room

## Architecture

The application follows a simplified architecture:

1. **Entry Point**: The `main.py` file initializes the LiveKit agent and runs it using the CLI.

2. **Agent Session**: The `session.py` file creates and configures the agent session with:

   - Speech-to-Text (Deepgram)
   - Language Model (OpenAI)
   - Text-to-Speech (OpenAI TTS)
   - Voice Activity Detection (Silero)
   - Turn Detection (Multilingual Model)

3. **Assistant**: The `assistant.py` file extends the LiveKit Agent class to:

   - Process voice input
   - Send messages to agent platform via Kafka
   - Receive responses and convert to speech
   - Handle LiveKit room communication

4. **Kafka Integration**: The `kafka/` directory handles:

   - Agent platform communication
   - Message sending and receiving
   - Response processing

5. **Configuration**: The `config.py` file manages environment variables and settings.

## Prerequisites

- Python 3.10+
- Poetry for dependency management
- Kafka instance for agent platform communication
- API keys for:
  - LiveKit
  - Deepgram
  - OpenAI

## Installation

### Using Poetry

```bash
# Install dependencies
poetry install --no-root

# Run the application
poetry run python main.py dev
```

### Using Docker

```bash
# Build the Docker image
docker build -t livekit-backend .

# Run the Docker container
docker run -p 8000:8000 --env-file .env livekit-backend
```

## Configuration

Copy the `.env.example` file to `.env` and fill in the required environment variables:

```env
LIVEKIT_URL=
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=

DEEPGRAM_API_KEY=

OPENAI_API_KEY=

KAFKA_BOOTSTRAP_SERVERS=
KAFKA_AGENT_CREATION_TOPIC=
KAFKA_AGENT_CHAT_TOPIC=
KAFKA_AGENT_RESPONSE_TOPIC=
KAFKA_CONSUMER_GROUP=
```

## Project Structure

```plaintext
livekit-backend/
├── app/                      # Application code
│   ├── core/                 # Core functionality
│   │   └── config.py         # Configuration settings
│   ├── db/                   # Database interactions
│   │   └── mongodb.py        # MongoDB client and operations
│   ├── schemas/              # Data models
│   │   ├── conversation.py   # Conversation schema
│   │   └── message.py        # Message schema
│   ├── services/             # Business logic
│   │   ├── conversation.py   # Conversation management
│   │   ├── metrics.py        # Usage metrics collection
│   │   └── session.py        # Agent session management
│   ├── assistant.py          # Assistant implementation
│   └── entrypoint.py         # Application entry point
├── KMS/                      # Kurento Media Server logs
├── Dockerfile                # Docker configuration
├── main.py                   # Main application file
├── pyproject.toml            # Poetry configuration
├── poetry.lock               # Poetry lock file
├── run_local.sh              # Script to run locally
└── .env                      # Environment variables
```

## Usage

### Running Locally

```bash
# Make the script executable
chmod +x run_local.sh

# Run the application
./run_local.sh
```

### Development

The application uses Poetry for dependency management:

```bash
# Add a new dependency
poetry add package-name

# Add a development dependency
poetry add --group dev package-name

# Update dependencies
poetry update
```

## Testing

```bash
# Run tests
poetry run pytest

# Run tests with coverage
poetry run pytest --cov=app
```

## License

[MIT License](LICENSE)
