apiVersion: v1
kind: ServiceAccount
metadata:
  name: livekit-backend-service-ai-sa
  namespace: ruh-catalyst
  labels:
    name: livekit-backend-service-ai-sa
    namespace: ruh-catalyst
    app: livekit-backend-service-ai
    deployment: livekit-backend-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: livekit-backend-service-ai-dp
  namespace: ruh-catalyst
  labels:
    name: livekit-backend-service-ai-dp
    namespace: ruh-catalyst
    app: livekit-backend-service-ai
    serviceaccount: livekit-backend-service-ai-sa
    deployment: livekit-backend-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: livekit-backend-service-ai
      deployment: livekit-backend-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-catalyst
        app: livekit-backend-service-ai
        deployment: livekit-backend-service-ai-dp
    spec:
      serviceAccountName: livekit-backend-service-ai-sa      
      containers:
      - name: livekit-backend-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50052
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: livekit-backend-service-ai-svc
  namespace: ruh-catalyst
spec:
  selector:
    app: livekit-backend-service-ai
    deployment: livekit-backend-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50052
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:livekit-backend-service-livekit-backend-hpa
#   namespace:ruh-catalyst
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:livekit-backend-service-livekit-backend-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---

