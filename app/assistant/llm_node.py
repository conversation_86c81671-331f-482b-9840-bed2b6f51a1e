# Standard Library Imports
import json
import logging
import string
from typing import Any, AsyncIterable

# Third Party Imports
from livekit.agents import llm

# Set up logging
logger = logging.getLogger(__name__)

# Constants for better performance and readability
ASSISTANT_ROLE = "assistant"
USER_ROLE = "user"
TRANSCRIPTION_TOPIC = "lk.transcription"
WORKFLOW_UPDATES_TOPIC = "lk.workflow.updates"
WORKFLOW_EXECUTION_TYPE = "workflow_execution"


# Helper function to process content
def _process_content(content: Any) -> str:
    """
    Process content efficiently based on its type.

    Args:
        content: The content to process.

    Returns:
        str: The processed content as a string.
    """

    # Join content if it's a list
    if isinstance(content, list):
        try:
            # Get the content from the first item
            content = json.loads(content[0]).get("content", "")

            # Strip all punctuation from the end and return
            return content.rstrip(string.punctuation)

        except json.JSONDecodeError:
            # Join the list elements with a space
            return " ".join(content)

    # Return the content as a string
    return str(content)


# Helper function to create response JSON
def _create_response_json(is_initial: bool, content: str) -> str:
    """
    Create a JSON response with consistent structure.

    Args:
        is_initial (bool): Whether this is an initial response.
        content (str): The content of the response.

    Returns:
        str: The JSON string.
    """

    # Remove all puctuations from the end
    content = content.rstrip(".,!?;:")

    # Create the JSON string
    return json.dumps(
        {"is_initial_response": is_initial, "content": content, "error": False}
    )


# Helper function to add to chat context
async def _add_to_chat_context(assistant, role: str, content: str) -> None:
    """
    Add a message to the processed chat context.

    Args:
        assistant: The Assistant instance.
        role (str): The role of the sender.
        content (str): The content of the message.
    """

    # Add the message to processed chat context
    assistant.processed_chat_ctx.append({"role": role, "content": content})


# Define the process_chat function
async def process_chat(assistant, chat_ctx: llm.ChatContext) -> AsyncIterable[Any]:
    """
    Process a chat request using the agent platform.

    Args:
        assistant: The Assistant instance.
        chat_ctx (llm.ChatContext): The chat context.

    Returns:
        AsyncIterable[Any]: The result of the chat processing.
    """

    # Process the last message content efficiently
    last_content = _process_content(chat_ctx.items[-1].content)

    # Add the last item to processed chat context
    await _add_to_chat_context(assistant, chat_ctx.items[-1].role, last_content)

    # Get conversation ID (use run_id as conversation ID)
    conversation_id = assistant.run_id

    try:
        # Traverse the chat request
        async for chunk in assistant.kafka_client.process_chat_request(
            chat_ctx, conversation_id
        ):
            # Handle regular responses
            if isinstance(chunk, dict) and "agent_response" in chunk:
                # Get content from response
                content = chunk["agent_response"].get("content", "")

                # Determine if this is an initial response (first few messages)
                is_initial = len(assistant.processed_chat_ctx) < 2

                # Create response JSON once
                response_json = _create_response_json(is_initial, content)

                # Yield the response
                yield response_json

                # Add the message to processed chat context
                await _add_to_chat_context(assistant, ASSISTANT_ROLE, content)

            # Handle string responses or unexpected formats
            elif isinstance(chunk, str):
                # Yield the string as is
                yield chunk

    except Exception as e:
        # Log the error
        logger.error(f"Error in process_chat: {str(e)}")

        # Yield error message to client
        yield _create_response_json(False, f"An error occurred: {str(e)}")


