# Standard Library Imports
import json
import logging
import string
from typing import Any, AsyncIterable

# Third Party Imports
from livekit.agents import llm

# Set up logging
logger = logging.getLogger(__name__)

# Constants for better performance and readability
ASSISTANT_ROLE = "assistant"
USER_ROLE = "user"
TRANSCRIPTION_TOPIC = "lk.transcription"
WORKFLOW_UPDATES_TOPIC = "lk.workflow.updates"
WORKFLOW_EXECUTION_TYPE = "workflow_execution"


# Helper function to process content
def _process_content(content: Any) -> str:
    """
    Process content efficiently based on its type.

    Args:
        content: The content to process.

    Returns:
        str: The processed content as a string.
    """

    # Join content if it's a list
    if isinstance(content, list):
        try:
            # Get the content from the first item
            content = json.loads(content[0]).get("content", "")

            # Strip all punctuation from the end and return
            return content.rstrip(string.punctuation)

        except json.JSONDecodeError:
            # Join the list elements with a space
            return " ".join(content)

    # Return the content as a string
    return str(content)


# Helper function to create response JSON
def _create_response_json(is_initial: bool, content: str) -> str:
    """
    Create a JSON response with consistent structure.

    Args:
        is_initial (bool): Whether this is an initial response.
        content (str): The content of the response.

    Returns:
        str: The JSON string.
    """

    # Remove all puctuations from the end
    content = content.rstrip(".,!?;:")

    # Create the JSON string
    return json.dumps(
        {"is_initial_response": is_initial, "content": content, "error": False}
    )


# Helper function to add to chat context
async def _add_to_chat_context(assistant, role: str, content: str) -> None:
    """
    Add a message to the processed chat context.

    Args:
        assistant: The Assistant instance.
        role (str): The role of the sender.
        content (str): The content of the message.
    """

    # Add the message to processed chat context
    assistant.processed_chat_ctx.append({"role": role, "content": content})


# Define the process_chat function
async def process_chat(assistant, chat_ctx: llm.ChatContext) -> AsyncIterable[Any]:
    """
    Process a chat request using the agent platform.

    Args:
        assistant: The Assistant instance.
        chat_ctx (llm.ChatContext): The chat context.

    Returns:
        AsyncIterable[Any]: The result of the chat processing.
    """

    # Cache frequently accessed values
    workflow_id = assistant.workflow.get("workflow_id") if assistant.workflow else None

    # Process the last message content efficiently
    last_content = _process_content(chat_ctx.items[-1].content)

    # Add the last item to processed chat context
    await _add_to_chat_context(assistant, chat_ctx.items[-1].role, last_content)

    # Check if we need to create or get a conversation
    chat_ctx_length = len(assistant.processed_chat_ctx)
    if chat_ctx_length > 3:
        # If conversation is None, create it
        if assistant.conversation_manager.conversation is None:
            # Get the 4th message for the title
            title = assistant.processed_chat_ctx[3]["content"]

            # Get the conversation
            await assistant.conversation_manager.get_conversation()

            # Update the conversation title
            await assistant.conversation_manager.update_conversation_title(title)

        # Store the user message
        await assistant.conversation_manager.store_user_message(
            last_content,
            workflow_id,
            None,
        )

    # Get conversation ID if available
    conversation_id = (
        assistant.conversation_manager.conversation.id
        if assistant.conversation_manager.conversation
        else None
    )

    try:
        # Traverse the chat request
        async for chunk in assistant.kafka_client.process_chat_request(
            chat_ctx, conversation_id
        ):
            # Check if workflow execution started
            if (
                isinstance(chunk, dict)
                and chunk.get("type") == WORKFLOW_EXECUTION_TYPE
                and chunk.get("success") is True
                and "correlation_id" in chunk
            ):
                # Get correlation ID, workflow ID and message
                correlation_id = chunk.get("correlation_id", "")
                workflow_id = chunk.get("workflow_id", "")
                message = chunk.get("message", "")

                # Set workflow ID
                assistant.workflow = {"workflow_id": workflow_id}

                # Set the workflow correlation ID
                assistant.kafka_client.workflow_correlation_id = correlation_id

                # Send the workflow update
                await assistant.room.local_participant.send_text(
                    _create_response_json(False, message),
                    topic=TRANSCRIPTION_TOPIC,
                )

                # Add the message to processed chat context
                await _add_to_chat_context(assistant, ASSISTANT_ROLE, message)

                try:
                    # Traverse the workflow responses
                    async for (
                        workflow_message
                    ) in assistant.kafka_client.listen_to_workflow_responses(
                        agent_platform=True,
                        workflow_correlation_id=correlation_id,
                    ):
                        # Send the workflow update
                        await assistant.room.local_participant.send_text(
                            workflow_message,
                            topic=WORKFLOW_UPDATES_TOPIC,
                        )

                        # Add the message to processed chat context
                        await _add_to_chat_context(
                            assistant, ASSISTANT_ROLE, workflow_message
                        )

                        try:
                            # Parse message once for efficiency
                            parsed_message = json.loads(workflow_message)

                            # Store in database
                            await (
                                assistant.conversation_manager.store_assistant_message(
                                    None,
                                    workflow_id,
                                    parsed_message,
                                )
                            )

                        except json.JSONDecodeError:
                            # Log the warning
                            logger.warning("Failed to parse workflow message as JSON")

                except Exception as e:
                    # Log the error
                    logger.error(f"Error processing workflow responses: {str(e)}")

            # Handle regular responses
            elif isinstance(chunk, dict) and "agent_response" in chunk:
                # Get content from response
                content = chunk["agent_response"].get("content", "")

                # Determine if this is an initial response
                is_initial = chat_ctx_length < 2

                # Create response JSON once
                response_json = _create_response_json(is_initial, content)

                # Yield the response
                yield response_json

                # Add the message to processed chat context
                await _add_to_chat_context(assistant, ASSISTANT_ROLE, content)

            # Handle string responses or unexpected formats
            elif isinstance(chunk, str):
                # Yield the string as is
                yield chunk

    except Exception as e:
        # Log the error
        logger.error(f"Error in process_chat: {str(e)}")

        # Yield error message to client
        yield _create_response_json(False, f"An error occurred: {str(e)}")


# Define the process_workflow_responses function
async def process_workflow_responses(assistant) -> AsyncIterable[Any]:
    """
    Process workflow responses.

    Args:
        assistant: The Assistant instance.

    Returns:
        AsyncIterable[Any]: The result of the workflow processing.
    """

    # Cache workflow ID for efficiency
    workflow_id = assistant.workflow.get("workflow_id") if assistant.workflow else None

    try:
        # Start workflow execution
        await assistant.kafka_client.start_workflow_execution(assistant.workflow)

        # Listen to workflow responses
        async for message in assistant.kafka_client.listen_to_workflow_responses():
            # Send the workflow update
            await assistant.room.local_participant.send_text(
                message,
                topic=WORKFLOW_UPDATES_TOPIC,
            )

            try:
                # Parse message once for efficiency
                parsed_message = json.loads(message)

                # Store in database
                await assistant.conversation_manager.store_assistant_message(
                    None,
                    workflow_id,
                    parsed_message,
                )

            except json.JSONDecodeError:
                # Log the warning
                logger.warning("Failed to parse workflow message as JSON")

    except Exception as e:
        # Log the error
        logger.error(f"Error in process_workflow_responses: {str(e)}")

        # Yield error message
        yield _create_response_json(False, f"Workflow error: {str(e)}")
