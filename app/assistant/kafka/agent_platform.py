# Standard Library Imports
import asyncio
import json
import logging
import time
from typing import Any, Dict

# Local Imports
from app.core.config import settings

# Set up logging
logger = logging.getLogger(__name__)

# Constants
MAX_RETRIES = 3
RETRY_DELAY = 1.0  # seconds
SINGLE_CHAT_TYPE = "single"
MULTI_CHAT_TYPE = "multi"
TRANSCRIPTION_TOPIC = "lk.transcription"


# Define the setup_agent_platform function
async def setup_agent_platform(client: Any) -> str:
    """
    Setup the agent platform with retry mechanism.

    This function assumes the Kafka client is already initialized.
    It implements a retry mechanism for better reliability.

    Args:
        client (Any): The KafkaAgentClient instance.

    Returns:
        str: The session ID.

    Raises:
        Exception: If agent platform setup fails after retries.
    """

    # Create the creation request based on chat type
    creation_request = _create_agent_request(client)

    # Create the headers (reuse client's cached headers if available)
    headers = getattr(client, "_headers", None) or [
        ("correlationId", client.run_id.encode("utf-8")),
        ("reply-topic", settings.KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
    ]

    # Setup retry mechanism
    retries = 0
    last_error = None

    # Retry loop
    while retries < MAX_RETRIES:
        try:
            # Send the creation request
            await client.client.send_message(
                settings.KAFKA_AGENT_CREATION_TOPIC, creation_request, headers
            )

            # Wait for response without timeout
            response = await client.client.listen_for_responses(
                correlation_id=client.run_id
            )

            # If no response received
            if not response:
                # Send user a message
                await client.room.local_participant.send_text(
                    json.dumps(
                        {
                            "is_initial_response": True,
                            "content": "No response received for agent creation",
                            "error": True,
                        }
                    ),
                    topic=TRANSCRIPTION_TOPIC,
                )

                # Break the loop
                break

            # Get the session ID from response
            session_id = response[0].get("session_id")
            if not session_id:
                # Send user a message
                await client.room.local_participant.send_text(
                    json.dumps(
                        {
                            "is_initial_response": True,
                            "content": "No session ID in response",
                            "error": True,
                        }
                    ),
                    topic=TRANSCRIPTION_TOPIC,
                )

                # Break the loop
                break

            # Log successful setup
            logger.debug(
                f"Agent platform setup successful with session ID: {session_id}"
            )

            # Return the session ID
            return session_id

        except Exception as e:
            # Store the error
            last_error = e

            # If not last retry
            retries += 1
            if retries < MAX_RETRIES:
                # Log the retry attempt
                logger.warning(
                    f"Agent platform setup failed (attempt {retries}/{MAX_RETRIES}): {str(e)}. Retrying..."
                )

                # Wait before retrying with exponential backoff
                await _sleep_with_backoff(retries)

            else:
                # Log the final failure
                logger.error(
                    f"Agent platform setup failed after {MAX_RETRIES} attempts: {str(e)}"
                )

    # If we get here, all retries failed
    raise last_error or Exception(
        "Failed to setup agent platform after multiple attempts"
    )


# Helper function to create the agent request
def _create_agent_request(client: Any) -> Dict[str, Any]:
    """
    Create the agent request based on chat type.

    Args:
        client (Any): The KafkaAgentClient instance.

    Returns:
        Dict[str, Any]: The creation request.
    """

    # Create the request based on chat type
    if client.chat_type == SINGLE_CHAT_TYPE:
        # Single chat type
        return {
            "agent_id": client.agent_id,
            "user_id": client.user_id,
            "communication_type": SINGLE_CHAT_TYPE,
            "run_id": client.run_id,
            "agent_group_id": None,
        }

    else:
        # Multi chat type
        return {
            "agent_id": None,
            "user_id": client.user_id,
            "communication_type": MULTI_CHAT_TYPE,
            "run_id": client.run_id,
            "agent_group_id": client.agent_id,
        }


# Helper function to sleep with backoff
async def _sleep_with_backoff(retry_count: int) -> None:
    """
    Sleep with exponential backoff.

    Args:
        retry_count (int): The current retry count.
    """

    # Calculate backoff time with jitter (0.8-1.2 multiplier for randomness)
    backoff_time = (
        RETRY_DELAY
        * (2 ** (retry_count - 1))
        * (0.8 + 0.4 * (hash(str(time.time())) % 100) / 100)
    )

    # Sleep for the calculated time
    await asyncio.sleep(backoff_time)
