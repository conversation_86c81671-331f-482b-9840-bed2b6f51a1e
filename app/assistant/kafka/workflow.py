# Standard Library Imports
import asyncio
import logging
import time
from typing import Any, AsyncIterable, Dict, Optional

# Third Party Imports
import httpx

# Local Imports
from app.core.config import settings

# Set up logging
logger = logging.getLogger(__name__)

# Constants
MAX_RETRIES = 3
RETRY_DELAY = 1.0  # seconds
MAX_CONSECUTIVE_ERRORS = 5
HTTP_TIMEOUT = 10.0  # HTTP request timeout

# HTTP client with connection pooling
_http_client: Optional[httpx.AsyncClient] = None


# HTTP client with connection pooling
async def get_http_client() -> httpx.AsyncClient:
    """
    Get or create a shared HTTP client with connection pooling.

    Returns:
        httpx.AsyncClient: The HTTP client instance.
    """

    # Get the global variable
    global _http_client

    # If client doesn't exist or is closed
    if _http_client is None or _http_client.is_closed:
        # Create a new client
        _http_client = httpx.AsyncClient(
            timeout=HTTP_TIMEOUT,
            limits=httpx.Limits(
                max_keepalive_connections=5, max_connections=10, keepalive_expiry=30.0
            ),
        )

    # Return the client
    return _http_client


# Close the HTTP client
async def close_http_client() -> None:
    """
    Close the shared HTTP client.
    """

    # Get the global variable
    global _http_client

    # If client exists and is not closed
    if _http_client and not _http_client.is_closed:
        # Close the client
        await _http_client.aclose()

        # Reset the global variable
        _http_client = None


# Start workflow execution
async def start_workflow_execution(
    workflow: Optional[Dict[str, Any]] = None,
) -> Optional[str]:
    """
    Start workflow execution with retry mechanism.

    Args:
        workflow (Optional[Dict[str, Any]], optional): The workflow to execute. Defaults to None.

    Returns:
        Optional[str]: The correlation ID if successful, None otherwise.

    Raises:
        Exception: If workflow execution fails after retries.
    """

    # If no workflow
    if not workflow:
        # Log the warning
        logger.warning("No workflow provided for execution")

        # Return None
        return None

    # Get the workflow ID for logging
    workflow_id = workflow.get("workflow_id", "unknown")

    # Implement retry mechanism
    retries = 0
    last_error = None

    # Retry loop
    while retries < MAX_RETRIES:
        try:
            # Get the shared HTTP client
            client = await get_http_client()

            # Log the request
            logger.debug(f"Starting workflow execution for workflow ID: {workflow_id}")

            # Send the workflow execution request
            response = await client.post(
                f"{settings.API_GATEWAY_BASE_URL}/api/v1/workflow-execute/execute",
                json=workflow,
                timeout=HTTP_TIMEOUT,
            )

            # If response is not 202
            if response.status_code != 202:
                # Raise exception
                raise Exception(
                    f"Failed to start workflow execution: HTTP {response.status_code}"
                )

            # Extract the correlation ID from the response
            correlation_id = response.json().get("correlationId")

            # If no correlation ID
            if not correlation_id:
                # Raise exception
                raise Exception("No correlation ID returned from workflow execution")

            # Log the success
            logger.debug(
                f"Workflow execution started with correlation ID: {correlation_id}"
            )

            # Return the correlation ID
            return correlation_id

        except Exception as e:
            # Store the error
            last_error = e

            # Log the retry attempt
            retries += 1
            if retries < MAX_RETRIES:
                # Log the retry attempt
                logger.warning(
                    f"Workflow execution failed (attempt {retries}/{MAX_RETRIES}): {str(e)}. Retrying..."
                )

                # Wait before retrying with exponential backoff
                await asyncio.sleep(RETRY_DELAY * (2 ** (retries - 1)))

            else:
                # Log the final failure
                logger.error(
                    f"Workflow execution failed after {MAX_RETRIES} attempts: {str(e)}"
                )

    # If we get here, all retries failed
    raise last_error or Exception(
        "Failed to start workflow execution after multiple attempts"
    )


# Listen to workflow responses
async def listen_to_workflow_responses(
    client: Any,
    agent_platform: bool = False,
    workflow_correlation_id: Optional[str] = None,
) -> AsyncIterable[Any]:
    """
    Listen for workflow responses using the workflow correlation ID with improved error handling.
    This is an async generator that yields messages from the Kafka topic.

    Args:
        client (Any): The KafkaAgentClient instance.
        agent_platform (bool, optional): Whether the response is from the agent platform. Defaults to False.
        workflow_correlation_id (Optional[str], optional): The workflow correlation ID. Defaults to None.

    Returns:
        AsyncIterable[Any]: An async generator yielding messages from the Kafka topic.

    Raises:
        Exception: If workflow correlation ID is required but not provided.
    """

    # Validate correlation ID
    if agent_platform and not workflow_correlation_id:
        # Raise exception
        raise Exception("Workflow correlation ID is required for agent platform")

    # Determine which correlation ID to use
    correlation_id = (
        workflow_correlation_id if agent_platform else client.workflow_correlation_id
    )

    # If no correlation ID
    if not correlation_id:
        # Log the error
        logger.error("No workflow correlation ID available")

        # Return
        return

    # Initialize counters and timers
    consecutive_errors = 0
    backoff_time = 0.1  # Initial backoff time in seconds

    # Verify consumer is initialized
    if not hasattr(client, "consumer") or client.consumer is None:
        # Log the error
        logger.error("Kafka consumer is not initialized")

        # Return
        return

    # Listen loop
    while True:
        try:
            # Use the get_message function from KafkaConsumerService
            message = await client.consumer.get_message(correlation_id)

            # If a message is received
            if message:
                # Reset error counter and backoff time
                consecutive_errors = 0
                backoff_time = 0.1

                # Yield the message
                yield message

            else:
                # If no message received
                await asyncio.sleep(backoff_time)

                # Increase backoff time
                backoff_time = min(backoff_time * 1.5, 5.0)

        except Exception as e:
            # Update error counter
            consecutive_errors += 1

            # Log the warning
            logger.warning(f"Error while listening for workflow responses: {str(e)}")

            # Apply backoff with jitter
            jitter = 0.1 * (hash(str(time.time())) % 10) / 10  # 0-10% jitter

            # Sleep for the calculated time
            await asyncio.sleep(backoff_time + jitter)

            # Increase backoff time
            backoff_time = min(backoff_time * 2, 5.0)
