# Standard Library Imports
import asyncio
import json
import logging
from functools import lru_cache
from typing import Any, AsyncIterable, Dict, List, Optional, Union

# Third Party Imports
from livekit.agents import ChatContext
from livekit.rtc import Room

# Local Imports
from app.assistant.kafka.agent_platform import setup_agent_platform
from app.assistant.kafka.workflow import (
    listen_to_workflow_responses,
    start_workflow_execution,
)
from app.core.config import settings
from app.kafka import KafkaConsumerService, KafkaTestClient

# Set up logging
logger = logging.getLogger(__name__)

# Constants
CHAT_CONTEXT_CACHE_SIZE = 32  # Cache size for processed chat contexts
MAX_INIT_RETRIES = 3  # Maximum number of initialization retries


# Helper function to process content
def _process_content(content: Any) -> str:
    """
    Process content efficiently based on its type.

    Args:
        content: The content to process.

    Returns:
        str: The processed content as a string.
    """

    # Join content if it's a list
    if isinstance(content, list):
        try:
            # Return the content field
            return json.loads(content[0]).get("content", "")

        except json.JSONDecodeError:
            # Join the list elements with a space
            return " ".join(content)

    # Return the content as a string
    return str(content)


# Kafka Agent Client
class KafkaAgentClient:
    """
    Kafka Agent Client for handling communication with Kafka.
    """

    # Initialize the Kafka Agent Client
    def __init__(
        self,
        room: Room,
        user_id: str,
        agent_id: str,
        run_id: str,
        chat_type: str = "single",
    ):
        """
        Initialize the Kafka Agent Client.

        Args:
            room (Room): The room for the agent.
            user_id (str): The user ID.
            agent_id (str): The agent ID.
            run_id (str): The run ID.
            chat_type (str, optional): The chat type (single or multi). Defaults to "single".
        """

        # Set the room
        self.room = room

        # Set the user ID
        self.user_id = user_id

        # Set the agent ID
        self.agent_id = agent_id

        # Set the run ID
        self.run_id = run_id

        # Set the chat type
        self.chat_type = chat_type

        # Set the session ID to None
        self.session_id = None

        # Initialize the workflow correlation ID
        self.workflow_correlation_id = None

        # Initialize the Kafka client
        self.client = KafkaTestClient()

        # Initialize the kafka consumer
        self.consumer = KafkaConsumerService()

        # Prepare common headers (cached)
        self._headers = [
            ("correlationId", self.run_id.encode("utf-8")),
            ("reply-topic", settings.KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

    # Initialize the Kafka client
    async def initialize(self) -> str:
        """
        Initialize the Kafka client and set up the agent platform.

        Returns:
            str: The session ID.
        """

        # Retry parameters for initialization
        retries = 0

        # While retries are less than the max retries
        while retries < MAX_INIT_RETRIES:
            try:
                # Initialize the Kafka client and consumer in parallel
                client_init_task = asyncio.create_task(self.client.initialize())
                consumer_init_task = asyncio.create_task(self.consumer.initialize())

                # Wait for both tasks to complete
                await asyncio.gather(client_init_task, consumer_init_task)

                # Verify Kafka client and consumer are properly initialized
                if not getattr(self.client, "_initialized", False):
                    raise Exception("Kafka client failed to initialize properly")

                if not getattr(self.consumer, "_initialized", False):
                    raise Exception("Kafka consumer failed to initialize properly")

                # Setup the agent platform
                self.session_id = await setup_agent_platform(self)

                # Return the session ID
                return self.session_id

            except Exception as e:
                # Log the error
                logger.error(f"Failed to initialize Kafka client: {str(e)}")

                # Increment retry counter
                retries += 1

                if retries < MAX_INIT_RETRIES:
                    # Calculate backoff time with exponential increase
                    backoff_time = 1.0 * (2 ** (retries - 1))

                    # Wait before retrying
                    await asyncio.sleep(backoff_time)

                else:
                    # Re-raise the error
                    raise

    # Process a chat request
    async def process_chat_request(
        self, chat_context: ChatContext, conversation_id: Optional[str] = None
    ) -> AsyncIterable[Union[Dict[str, Any], str]]:
        """Process a chat request by sending it to the Kafka topic and waiting for a response.

        This method handles the communication with the backend services through Kafka.
        It formats the chat context, sends the request, and processes the response.

        Args:
            chat_context (ChatContext): The chat context containing the conversation history.
            conversation_id (Optional[str]): The conversation ID. Defaults to None.

        Returns:
            AsyncIterable[Union[Dict[str, Any], str]]: The responses from the agent platform.
        """

        try:
            # Process chat context efficiently
            processed_chat_ctx = self._process_chat_context(chat_context)

            # Prepare the chat request payload
            chat_request = {
                "run_id": self.run_id,
                "session_id": self.session_id,
                "chat_context": processed_chat_ctx,
                "conversation_id": conversation_id if conversation_id else self.run_id,
            }

            # Send chat request using cached headers
            await self.client.send_message(
                settings.KAFKA_AGENT_CHAT_TOPIC, chat_request, self._headers
            )

            # Wait for and process responses with timeout
            responses = await self.client.listen_for_responses(
                correlation_id=self.run_id
            )

            # If responses received
            if responses:
                # Yield responses for current run
                for response in responses:
                    # Check if response is for the current run
                    if response.get("run_id") == self.run_id:
                        # Yield the response
                        yield response

            else:
                # Yield no response received
                yield {
                    "error": "No response received from agent",
                    "agent_response": {"content": "No response received from agent"},
                }

        except Exception as e:
            # Log the error
            logger.error(f"Error processing chat request: {str(e)}")

            # Return error message in the expected format
            yield {"error": str(e), "agent_response": {"content": f"Error: {str(e)}"}}

    # Process chat context
    @lru_cache(maxsize=CHAT_CONTEXT_CACHE_SIZE)
    def _process_chat_context(self, chat_context: ChatContext) -> List[Dict[str, str]]:
        """
        Process chat context efficiently with caching.

        Args:
            chat_context (ChatContext): The chat context to process.

        Returns:
            List[Dict[str, str]]: The processed chat context.
        """

        # Pre-allocate the list with the correct size
        result = []

        # Process each item
        for item in chat_context.items:
            # Add the item to the result
            result.append(
                {
                    "role": item.role,
                    "content": _process_content(item.content),
                }
            )

        # Return the result
        return result

    # Start workflow execution
    async def start_workflow_execution(
        self, workflow: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Start workflow execution.

        Args:
            workflow (Optional[Dict[str, Any]], optional): The workflow to execute. Defaults to None.
        """

        try:
            # Ensure client is initialized
            if not getattr(self.client, "_initialized", False) or not getattr(
                self.consumer, "_initialized", False
            ):
                # Initialize the client and consumer
                await self.initialize()

            # Call the start_workflow_execution function
            self.workflow_correlation_id = await start_workflow_execution(workflow)

            # If no correlation ID returned
            if not self.workflow_correlation_id:
                # Log the warning
                logger.warning("No correlation ID returned from workflow execution")

        except Exception as e:
            # Log the error
            logger.error(f"Error starting workflow execution: {str(e)}")

            # Re-raise the error
            raise

    # Listen to workflow responses
    async def listen_to_workflow_responses(
        self,
        agent_platform: bool = False,
        workflow_correlation_id: Optional[str] = None,
    ) -> AsyncIterable[Any]:
        """
        Listen for workflow responses using the workflow correlation ID.
        This is an async generator that yields messages from the Kafka topic.

        Args:
            agent_platform (bool, optional): Whether the response is from the agent platform. Defaults to False.
            workflow_correlation_id (Optional[str], optional): The workflow correlation ID. Defaults to None.

        Returns:
            AsyncIterable[Any]: An async generator yielding messages from the Kafka topic.
        """

        try:
            # Ensure client is initialized
            if not getattr(self.consumer, "_initialized", False):
                # Initialize the consumer
                await self.initialize()

            # Call the listen_to_workflow_responses function
            async for message in listen_to_workflow_responses(
                self, agent_platform, workflow_correlation_id
            ):
                # Yield the message
                yield message

        except Exception as e:
            # Log the error
            logger.error(f"Error listening to workflow responses: {str(e)}")

    # Cleanup resources
    async def cleanup(self) -> None:
        """
        Cleanup resources used by the client.
        """
        try:
            # Clear any cached data
            if hasattr(self, "_process_chat_context"):
                # Clear the cache
                self._process_chat_context.cache_clear()

            # If client exists
            if self.client:
                # Cleanup the client
                await self.client.cleanup()

            # If consumer exists
            if self.consumer:
                # Stop all consumer services
                await self.consumer.stop_services()

            # Reset state
            self.session_id = None
            self.workflow_correlation_id = None

        except Exception as e:
            # Log the error
            logger.error(f"Error during cleanup: {str(e)}")
