# Standard Library Imports
import json
import logging
from typing import AsyncIterable, List

# Set up logging
logger = logging.getLogger(__name__)

# Constants
MAX_BUFFER_SIZE = 1024 * 1024  # 1MB maximum buffer size


# Define the process_transcription function
async def process_transcription(
    assistant, text: AsyncIterable[str]
) -> AsyncIterable[str]:
    """
    Process transcription efficiently.

    Args:
        assistant: The Assistant instance.
        text (AsyncIterable[str]): The text to transcribe.

    Returns:
        AsyncIterable[str]: The transcribed text.
    """

    # Initialize buffer with estimated size
    buffer: List[str] = []
    buffer_size = 0

    # Cache workflow ID for efficiency
    workflow_id = assistant.workflow.get("workflow_id") if assistant.workflow else None

    # Iterate over the text chunks
    async for chunk in text:
        # Only process non-empty chunks
        if chunk and chunk.strip():
            # Get the size of the chunk
            chunk_size = len(chunk)

            # Check buffer size to prevent memory issues
            if buffer_size + chunk_size > MAX_BUFFER_SIZE:
                # Log the warning
                logger.warning(
                    f"Transcription buffer exceeded {MAX_BUFFER_SIZE} bytes, truncating"
                )

                # Keep the buffer but don't add more
                pass

            else:
                # Add the chunk to the buffer
                buffer.append(chunk)

                # Update buffer size
                buffer_size += chunk_size

        # Always yield the chunk to maintain streaming behavior
        yield chunk

    # Early exit if no conversation or empty buffer
    if not assistant.conversation_manager.conversation or not buffer:
        # Return
        return

    # Process the complete message
    try:
        # Join buffer efficiently
        full_content = "".join(buffer)

        # Try to parse as JSON
        try:
            # Extract content from JSON
            content = json.loads(full_content).get("content")

            # If valid JSON
            if content:
                # Store the content
                await assistant.conversation_manager.store_assistant_message(
                    content,
                    workflow_id,
                    None,
                )

        except json.JSONDecodeError:
            # Log the warning
            logger.debug("Transcription is not valid JSON, storing raw content")

            # Store the raw content
            await assistant.conversation_manager.store_assistant_message(
                full_content,
                workflow_id,
                None,
            )

    except Exception as e:
        # Log the error but don't crash
        logger.error(f"Error processing transcription: {str(e)}")
