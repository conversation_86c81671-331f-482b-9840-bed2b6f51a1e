# Standard Library Imports
from typing import AsyncIterable


# Define the process_transcription function
async def process_transcription(
    assistant, text: AsyncIterable[str]
) -> AsyncIterable[str]:
    """
    Process transcription efficiently.

    Args:
        assistant: The Assistant instance.
        text (AsyncIterable[str]): The text to transcribe.

    Returns:
        AsyncIterable[str]: The transcribed text.
    """

    # Iterate over the text chunks and yield them directly
    async for chunk in text:
        # Always yield the chunk to maintain streaming behavior
        yield chunk
