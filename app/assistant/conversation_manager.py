# Standard Library Imports
import json
from typing import Any, List, Optional

# Third-Party Imports
from livekit import rtc

# Local Imports
from app.db import get_conversation, insert_message
from app.db.conversation import update_conversation_title

# Constants for better performance and readability
TRANSCRIPTION_TOPIC = "lk.transcription"


# Conversation Manager
class ConversationManager:
    """
    Conversation Manager for handling conversation-related operations.
    """

    # Initialize the Conversation Manager
    def __init__(
        self,
        room: rtc.Room,
        user_id: str,
        agent_id: str,
        run_id: str,
        chat_type: str = "single",
    ):
        """
        Initialize the Conversation Manager.

        Args:
            room (rtc.Room): The room for the agent.
            user_id (str): The user ID.
            agent_id (str): The agent ID.
            run_id (str): The run ID.
            chat_type (str, optional): The chat type (single or multi). Defaults to "single".
            conversation (Conversation, optional): The conversation. Defaults to None.
        """

        # Set the room
        self.room = room

        # Set the user ID
        self.user_id = user_id

        # Set the agent ID
        self.agent_id = agent_id

        # Set the run ID
        self.run_id = run_id

        # Set the chat type
        self.chat_type = chat_type

        # Set the conversation to None
        self.conversation = None

    # Get or create a conversation
    async def get_conversation(self) -> None:
        """
        Get a conversation for the user.

        This method checks if a conversation exists for the user_id, agent_id, and run_id.
        If it exists, it gets the conversation; otherwise, it raises an error.

        Raises:
            ValueError: If the conversation does not exist.

        """

        try:
            # Try to get the existing conversation
            conversation = await get_conversation(
                conversationId=self.run_id, agentId=self.agent_id, userId=self.user_id
            )

            # If conversation exists
            if conversation:
                # Set the conversation
                self.conversation = conversation

            else:
                # Send user a message
                await self.room.local_participant.send_text(
                    json.dumps(
                        {
                            "is_initial_response": True,
                            "content": "No conversation found",
                            "error": True,
                        }
                    ),
                    topic=TRANSCRIPTION_TOPIC,
                )

                # Raise an error if conversation does not exist
                raise ValueError(f"Conversation with ID {self.run_id} not found")

        except Exception as e:
            # Set conversation to None
            self.conversation = None

            # Send user a message
            await self.room.local_participant.send_text(
                json.dumps(
                    {
                        "is_initial_response": True,
                        "content": "No conversation found",
                        "error": True,
                    }
                ),
                topic=TRANSCRIPTION_TOPIC,
            )

            # Raise the error
            raise e

    # Store a user message
    async def store_user_message(
        self,
        content: Optional[str | List[str]] = None,
        workflow_id: str = None,
        workflow_response: dict[str, Any] = None,
    ) -> None:
        """
        Store a user message in the database.

        Args:
            content (str): The content of the message.
            workflow_id (str, optional): The ID of the workflow. Defaults to None.
        """

        # If conversation exists
        if self.conversation:
            try:
                # Insert the message
                await insert_message(
                    conversationId=self.conversation.id,
                    role="user",
                    content=content,
                    workflowId=workflow_id,
                    workflowResponse=workflow_response,
                )

            except Exception as e:
                # Send user a message
                await self.room.local_participant.send_text(
                    json.dumps(
                        {
                            "is_initial_response": True,
                            "content": "Error storing user message",
                            "error": True,
                        }
                    ),
                    topic=TRANSCRIPTION_TOPIC,
                )

                # Raise the error
                raise e

    # Store an assistant message
    async def store_assistant_message(
        self,
        content: Optional[str | List[str]] = None,
        workflow_id: str = None,
        workflow_response: dict[str, Any] = None,
    ) -> None:
        """
        Store an assistant message in the database.

        Args:
            content (str): The content of the message.
            workflow_id (str, optional): The ID of the workflow. Defaults to None.
        """

        # If conversation exists
        if self.conversation:
            try:
                # Insert the message
                await insert_message(
                    conversationId=self.conversation.id,
                    role="assistant",
                    content=content,
                    workflowId=workflow_id,
                    workflowResponse=workflow_response,
                )

            except Exception as e:
                # Send user a message
                await self.room.local_participant.send_text(
                    json.dumps(
                        {
                            "is_initial_response": True,
                            "content": "Error storing assistant message",
                            "error": True,
                        }
                    ),
                    topic=TRANSCRIPTION_TOPIC,
                )

                # Raise the error
                raise e

    # Update the title of the conversation
    async def update_conversation_title(self, title: str) -> None:
        """
        Update the title of the conversation.

        Args:
            title (str): The new title.

        Raises:
            Exception: If there is an error updating the title.
        """

        try:
            # Update the conversation title using run_id as conversationId
            await update_conversation_title(self.run_id, title)

            # If conversation exists
            if self.conversation:
                # Update the conversation title
                self.conversation.title = title

        except Exception as e:
            # Send user a message
            await self.room.local_participant.send_text(
                json.dumps(
                    {
                        "is_initial_response": True,
                        "content": "Error updating conversation title",
                        "error": True,
                    }
                ),
                topic=TRANSCRIPTION_TOPIC,
            )

            # Raise the error
            raise e


# Export the ConversationManager class
__all__ = ["ConversationManager"]
