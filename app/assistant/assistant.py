# Standard Library Imports
import json
import logging
from typing import Any, AsyncGenerator, AsyncIterable, Coroutine, Dict, List

# Third Party Imports
from livekit import rtc
from livekit.agents import (
    Agent,
    ChatContext,
    FunctionTool,
    ModelSettings,
    llm,
)
from livekit.rtc import Room

# Local Imports
from app.assistant.kafka import KafkaAgentClient
from app.assistant.llm_node import process_chat
from app.assistant.transcription_node import process_transcription

# Set up logging
logger = logging.getLogger(__name__)


# Assistant Class
class Assistant(Agent):
    """
    Assistant class that extends the LiveKit Agent.
    """

    # Initialize the Assistant class
    def __init__(
        self,
        room: Room,
        instructions: str,
        chat_ctx: ChatContext,
        user_id: str,
        agent_id: str,
        run_id: str,
        chat_type: str = "single",
    ) -> None:
        """
        Initialize the Assistant class.

        Args:
            room (Room): The room for the agent.
            instructions (str): The instructions for the agent.
            chat_ctx (ChatContext): The chat context for the agent.
            user_id (str): The user ID for the agent.
            agent_id (str): The agent ID for the agent.
            run_id (str): The run ID for the agent.
            chat_type (str, optional): The chat type (single or multi). Defaults to "single".
        """

        # Initialize the parent class
        super().__init__(
            instructions=instructions,
            chat_ctx=chat_ctx,
        )

        # Set the room
        self.room = room

        # Set the user ID
        self.user_id = user_id

        # Set the agent ID
        self.agent_id = agent_id

        # Set the run ID
        self.run_id = run_id

        # Set the chat type
        self.chat_type = chat_type

        # Initialize processed chat context (will be populated in initialize)
        self.processed_chat_ctx: List[Dict[str, str]] = []

        # Initialize the Kafka client
        self.kafka_client = KafkaAgentClient(
            room=self.room,
            user_id=self.user_id,
            agent_id=self.agent_id,
            run_id=self.run_id,
            chat_type=self.chat_type,
        )

        # Store active tasks to prevent garbage collection
        self._active_tasks = set()

    # Initialize the assistant
    async def initialize(self) -> str:
        """
        Initialize the assistant.

        This method initializes the Kafka client and sets up the agent platform.

        Returns:
            str: The session ID.
        """

        # Process the chat context efficiently
        self._process_chat_context()

        # Initialize the Kafka client
        session_id = await self.kafka_client.initialize()

        # Return the session ID
        return session_id

    # Helper method to process chat context
    def _process_chat_context(self) -> None:
        """
        Process the chat context efficiently.

        This helper method converts the chat context items to a format
        suitable for storage and processing.
        """

        # Pre-allocate the list with the correct size for better performance
        self.processed_chat_ctx = []

        # Process each item in the chat context
        for item in self.chat_ctx.items:
            # Join content efficiently based on type
            content = item.content
            if isinstance(content, list):
                # Only join if it's actually a list
                content_str = " ".join(content)

            else:
                # Already a string
                content_str = str(content)

            # Add to processed context
            self.processed_chat_ctx.append(
                {
                    "role": item.role,
                    "content": content_str,
                }
            )

    # LLM node
    def llm_node(
        self,
        chat_ctx: llm.ChatContext,
        tools: list[FunctionTool],
        model_settings: ModelSettings,
    ) -> AsyncIterable[llm.ChatChunk | str]:
        """LLM node.

        Args:
            self (Assistant): The assistant instance.
            chat_ctx (llm.ChatContext): The chat context.
            tools (list[FunctionTool]): The tools available to the agent.
            model_settings (ModelSettings): The model settings.

        Returns:
            AsyncIterable[llm.ChatChunk | str]: The result of the LLM node.
        """

        # Return the process_chat function for normal chat
        return process_chat(self, chat_ctx)

    # Transcription node
    def transcription_node(
        self, text: AsyncIterable[str], model_settings: ModelSettings
    ) -> AsyncIterable[str]:
        """Transcription node.

        Args:
            self (Assistant): The assistant instance.
            text (AsyncIterable[str]): The text to transcribe.
            model_settings (ModelSettings): The model settings.

        Returns:
            AsyncIterable[str]: The transcribed text.
        """

        # Return the process_transcription function
        return process_transcription(self, text)

    # TTS node
    def tts_node(
        self, text: AsyncIterable[str], model_settings: ModelSettings
    ) -> (
        AsyncGenerator[rtc.AudioFrame, None]
        | Coroutine[Any, Any, AsyncIterable[rtc.AudioFrame]]
        | Coroutine[Any, Any, None]
    ):
        # Function to process the text
        async def process_text(text: str) -> AsyncIterable[str]:
            # Initialize the buffer
            buffer = []

            # Iterate over the text chunks
            async for chunk in text:
                # Only process non-empty chunks
                if chunk and chunk.strip():
                    # Add the chunk to the buffer
                    buffer.append(chunk)

            # Combine the buffer into a single string
            buffer = " ".join(buffer)

            # Load the buffer as json
            buffer = json.loads(buffer)

            # Extract the content
            content = buffer.get("content")

            # Yield the content
            yield content

        # Return the default TTS node
        return Agent.default.tts_node(self, process_text(text), model_settings)

    # Cleanup resources
    async def cleanup(self) -> None:
        """
        Cleanup resources used by the assistant.
        """
        try:
            # Cleanup the Kafka client
            await self.kafka_client.cleanup()

            # Clear any cached data
            self.processed_chat_ctx = []

        except Exception as e:
            # Log any cleanup errors but don't raise
            logger.error(f"Error during assistant cleanup: {str(e)}")
