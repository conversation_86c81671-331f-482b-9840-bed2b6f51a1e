# Standard Library Imports
import asyncio
import json
import logging
from typing import Any, AsyncGenerator, AsyncIterable, Coroutine, Dict, List, Optional

# Third Party Imports
import httpx
from livekit import rtc
from livekit.agents import (
    Agent,
    BackgroundAudioPlayer,
    ChatContext,
    FunctionTool,
    ModelSettings,
    llm,
)
from livekit.rtc import Room

# Local Imports
from app.assistant.conversation_manager import ConversationManager
from app.assistant.kafka import KafkaAgentClient
from app.assistant.llm_node import process_chat, process_workflow_responses
from app.assistant.transcription_node import process_transcription
from app.core.config import settings
from app.db.client import close_connections

# Set up logging
logger = logging.getLogger(__name__)

# Constants for better performance and readability
WORKFLOW_APPROVAL_TOPIC = "lk.workflow.approval"
WORKFLOW_REEXECUTE_TOPIC = "lk.workflow.reexecute"


# Assistant Class
class Assistant(Agent):
    """
    Assistant class that extends the LiveKit Agent.
    """

    # Initialize the Assistant class
    def __init__(
        self,
        room: Room,
        instructions: str,
        chat_ctx: ChatContext,
        user_id: str,
        agent_id: str,
        run_id: str,
        chat_type: str = "single",
    ) -> None:
        """
        Initialize the Assistant class.

        Args:
            room (Room): The room for the agent.
            instructions (str): The instructions for the agent.
            chat_ctx (ChatContext): The chat context for the agent.
            user_id (str): The user ID for the agent.
            agent_id (str): The agent ID for the agent.
            run_id (str): The run ID for the agent.
            chat_type (str, optional): The chat type (single or multi). Defaults to "single".
        """

        # Initialize the parent class
        super().__init__(
            instructions=instructions,
            chat_ctx=chat_ctx,
        )

        # Set the room
        self.room = room

        # Set the user ID
        self.user_id = user_id

        # Set the agent ID
        self.agent_id = agent_id

        # Set the run ID
        self.run_id = run_id

        # Set the chat type
        self.chat_type = chat_type

        # Initialize processed chat context (will be populated in initialize)
        self.processed_chat_ctx: List[Dict[str, str]] = []

        # Initialize the Kafka client
        self.kafka_client = KafkaAgentClient(
            room=self.room,
            user_id=self.user_id,
            agent_id=self.agent_id,
            run_id=self.run_id,
            chat_type=self.chat_type,
        )

        # Initialize the conversation manager
        self.conversation_manager = ConversationManager(
            room=self.room,
            user_id=self.user_id,
            agent_id=self.agent_id,
            run_id=self.run_id,
            chat_type=self.chat_type,
        )

        # Initialize workflow to None
        self.workflow: Optional[Dict] = None

        # Initialize background audio to None
        self.background_audio: Optional[BackgroundAudioPlayer] = None

        # Store active tasks to prevent garbage collection
        self._active_tasks = set()

    # Register a text stream handler for the topic 'lk.workflow.approval'
    def register_workflow_approval_handler(self) -> None:
        """
        Register a text stream handler for the 'lk.workflow.approval' topic.

        This handler uses Option 2: it reads the entire text stream after completion.

        The handler currently does not implement any processing logic.
        """

        async def async_handle_text_stream(reader, participant_identity):
            """
            Asynchronously handle the text stream by reading the entire text after stream completion.

            Args:
                reader: The text stream reader.
                participant_identity: The identity of the participant sending the stream.
            """

            # Get the entire text after the stream completes.
            text = await reader.read_all()

            try:
                # Prepare the API request payload
                payload = {
                    "correlationId": self.kafka_client.workflow_correlation_id,
                    "decision": json.loads(text).get("decision", "reject"),
                }

            except json.JSONDecodeError:
                # Prepare the API request payload with default decision
                payload = {
                    "correlationId": self.kafka_client.workflow_correlation_id,
                    "decision": "reject",
                }

            try:
                # Create a new HTTP client
                async with httpx.AsyncClient() as client:
                    # Make the POST request to the approval endpoint
                    await client.post(
                        f"{settings.API_GATEWAY_BASE_URL}/api/v1/workflow-execute/approve",
                        json=payload,
                        timeout=10.0,
                    )

            except Exception as e:
                # Log any exceptions during the API call
                print(f"Error making approval API request: {str(e)}")

        def handle_text_stream(reader, participant_identity):
            """
            Handle the text stream by creating an asyncio task to process it asynchronously.

            Args:
                reader: The text stream reader.
                participant_identity: The identity of the participant sending the stream.
            """

            # Create the task
            task = asyncio.create_task(
                async_handle_text_stream(reader, participant_identity)
            )

            # Add the task to the active tasks set
            self._active_tasks.add(task)

            # Add the done callback
            task.add_done_callback(lambda t: self._active_tasks.remove(t))

        # Register the handler with the room for the topic 'lk.workflow.approval'
        self.room.register_text_stream_handler(
            WORKFLOW_APPROVAL_TOPIC, handle_text_stream
        )

    # Register a text stream handler for the topic 'lk.workflow.reexecute'
    def register_workflow_reexecute_handler(self) -> None:
        """
        Register a text stream handler for the 'lk.workflow.reexecute' topic.

        This is a placeholder for future implementation.
        """

        async def async_handle_text_stream(reader, participant_identity):
            """
            Asynchronously handle the text stream for the reexecute topic.
            Placeholder for future implementation.

            Args:
                reader: The text stream reader.
                participant_identity: The identity of the participant sending the stream.
            """

            # Get the entire text after the stream completes.
            text = await reader.read_all()

            # Load text as json
            payload = json.loads(text)

            # Add correlation id to payload
            payload["correlationId"] = self.kafka_client.workflow_correlation_id

            # Make api request
            async with httpx.AsyncClient() as client:
                # Make the POST request to the re-execute endpoint
                await client.post(
                    f"{settings.API_GATEWAY_BASE_URL}/api/v1/workflow-execute/re-execute",
                    json=payload,
                    timeout=10.0,
                )

        def handle_text_stream(reader, participant_identity):
            """
            Handle the text stream by creating an asyncio task to process it asynchronously.
            Placeholder for future implementation.

            Args:
                reader: The text stream reader.
                participant_identity: The identity of the participant sending the stream.
            """

            # Create the task
            task = asyncio.create_task(
                async_handle_text_stream(reader, participant_identity)
            )

            # Add the task to the active tasks set
            self._active_tasks.add(task)

            # Add the done callback
            task.add_done_callback(lambda t: self._active_tasks.remove(t))

        # Register the handler with the room for the topic 'lk.workflow.reexecute'
        self.room.register_text_stream_handler(
            WORKFLOW_REEXECUTE_TOPIC, handle_text_stream
        )

    # Initialize the assistant
    async def initialize(self) -> str:
        """
        Initialize the assistant.

        This method initializes the Kafka client, sets up the agent platform,
        and processes the initial chat context.

        Returns:
            str: The session ID.
        """

        # Process the chat context efficiently
        self._process_chat_context()

        # Initialize the Kafka client
        session_id = await self.kafka_client.initialize()

        # Register the workflow approval handler
        self.register_workflow_approval_handler()

        # Register the workflow re-execute handler
        self.register_workflow_reexecute_handler()

        # Return the session ID
        return session_id

    # Helper method to process chat context
    def _process_chat_context(self) -> None:
        """
        Process the chat context efficiently.

        This helper method converts the chat context items to a format
        suitable for storage and processing.
        """

        # Pre-allocate the list with the correct size for better performance
        self.processed_chat_ctx = []

        # Process each item in the chat context
        for item in self.chat_ctx.items:
            # Join content efficiently based on type
            content = item.content
            if isinstance(content, list):
                # Only join if it's actually a list
                content_str = " ".join(content)

            else:
                # Already a string
                content_str = str(content)

            # Add to processed context
            self.processed_chat_ctx.append(
                {
                    "role": item.role,
                    "content": content_str,
                }
            )

    # LLM node
    def llm_node(
        self,
        chat_ctx: llm.ChatContext,
        tools: list[FunctionTool],
        model_settings: ModelSettings,
    ) -> AsyncIterable[llm.ChatChunk | str]:
        """LLM node.

        Args:
            self (Assistant): The assistant instance.
            chat_ctx (llm.ChatContext): The chat context.
            tools (list[FunctionTool]): The tools available to the agent.
            model_settings (ModelSettings): The model settings.

        Returns:
            AsyncIterable[llm.ChatChunk | str]: The result of the LLM node.
        """

        # Reset workflow for this request
        self.workflow = None

        # Get the last message content
        last_content = (
            chat_ctx.items[-1].content[-1]
            if chat_ctx.items and chat_ctx.items[-1].content
            else ""
        )

        # Only try to parse as JSON if it looks like JSON (starts with '{')
        if isinstance(last_content, str) and last_content.strip().startswith("{"):
            try:
                # Try to load the last message content as JSON
                last_message_content = json.loads(last_content)

                # Get the workflow from the last message content
                if isinstance(last_message_content, dict):
                    # Get the workflow
                    workflow = last_message_content.get("workflow")

                    if workflow:
                        # Set the workflow
                        self.workflow = workflow

                        # Update the last message content
                        chat_ctx.items[-1].content[-1] = last_message_content.get(
                            "content", ""
                        )
                        logger.debug(
                            f"Workflow detected: {workflow.get('workflow_id', 'unknown')}"
                        )

            except json.JSONDecodeError:
                # Log the error but continue processing
                logger.debug(
                    "Failed to parse message as JSON, continuing with normal processing"
                )

            except Exception as e:
                # Log any other errors but continue
                logger.warning(f"Error processing potential workflow message: {str(e)}")

        # Process based on workflow presence
        if self.workflow is None:
            # Return the process_chat function for normal chat
            return process_chat(self, chat_ctx)

        else:
            # Return the process_workflow_responses function for workflow
            return process_workflow_responses(self)

    # Transcription node
    def transcription_node(
        self, text: AsyncIterable[str], model_settings: ModelSettings
    ) -> AsyncIterable[str]:
        """Transcription node.

        Args:
            self (Assistant): The assistant instance.
            text (AsyncIterable[str]): The text to transcribe.
            model_settings (ModelSettings): The model settings.

        Returns:
            AsyncIterable[str]: The transcribed text.
        """

        # Return the process_transcription function
        return process_transcription(self, text)

    # TTS node
    def tts_node(
        self, text: AsyncIterable[str], model_settings: ModelSettings
    ) -> (
        AsyncGenerator[rtc.AudioFrame, None]
        | Coroutine[Any, Any, AsyncIterable[rtc.AudioFrame]]
        | Coroutine[Any, Any, None]
    ):
        # Function to process the text
        async def process_text(text: str) -> AsyncIterable[str]:
            # Initialize the buffer
            buffer = []

            # Iterate over the text chunks
            async for chunk in text:
                # Only process non-empty chunks
                if chunk and chunk.strip():
                    # Add the chunk to the buffer
                    buffer.append(chunk)

            # Combine the buffer into a single string
            buffer = " ".join(buffer)

            # Load the buffer as json
            buffer = json.loads(buffer)

            # Extract the content
            content = buffer.get("content")

            # Yield the content
            yield content

        # Return the default TTS node
        return Agent.default.tts_node(self, process_text(text), model_settings)

    # Cleanup resources
    async def cleanup(self) -> None:
        """
        Cleanup resources used by the assistant.
        """
        try:
            # First clean up background audio to prevent event loop issues
            if self.background_audio is not None:
                try:
                    # Stop background audio
                    self.background_audio = None
                except Exception as e:
                    # Log but continue with cleanup
                    logger.error(f"Error stopping background audio: {str(e)}")

            # Cleanup the Kafka client
            await self.kafka_client.cleanup()

            # Close MongoDB connections for this event loop
            await close_connections()

            # Clear any cached data
            self.processed_chat_ctx = []
            self.workflow = None

        except Exception as e:
            # Log any cleanup errors but don't raise
            logger.error(f"Error during assistant cleanup: {str(e)}")
