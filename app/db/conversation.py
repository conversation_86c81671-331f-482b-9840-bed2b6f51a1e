# Standard Library Imports
import logging
from datetime import datetime
from typing import List, Optional

# Third Party Imports
from pymongo.errors import PyMongoError

# Local Imports
from app.db.client import get_conversation_collection
from app.schemas import Conversation

# Set up logging
logger = logging.getLogger(__name__)


# Get a conversation
async def get_conversation(
    conversationId: str,
    agentId: str,
    userId: str,
) -> Optional[Conversation]:
    """
    Get a conversation by ID.

    Args:
        conversationId (str): The ID of the conversation.
        agentId (str): The ID of the agent.
        userId (str): The ID of the user.

    Returns:
        Optional[Conversation]: The conversation if found, None otherwise.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    try:
        # Get the collection
        collection = await get_conversation_collection()

        # Get the conversation - using string ID directly
        conversation = await collection.find_one(
            {"_id": conversationId, "agentId": agentId, "userId": userId}
        )

        # Return the conversation
        if conversation:
            # Return the conversation
            return Conversation(**conversation)

        # Return None if conversation not found
        return None

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error getting conversation: {str(e)}")

        # Re-raise the error
        raise


# Update a conversation
async def update_conversation_summary(conversationId: str, summary: str) -> None:
    """
    Update the summary of a conversation.

    Args:
        conversationId (str): The ID of the conversation.
        summary (str): The new summary.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    try:
        # Get the collection
        collection = await get_conversation_collection()

        # Update the conversation
        await collection.update_one(
            {"_id": conversationId},
            {"$set": {"summary": summary, "updatedAt": datetime.utcnow()}},
        )

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error updating conversation summary: {str(e)}")

        # Re-raise the error
        raise


# Get conversations by user
async def get_conversations_by_user(
    userId: str, limit: int = 10, skip: int = 0
) -> List[Conversation]:
    """
    Get conversations by user ID.

    Args:
        userId (str): The ID of the user.
        limit (int, optional): The maximum number of conversations to return. Defaults to 10.
        skip (int, optional): The number of conversations to skip. Defaults to 0.

    Returns:
        List[Conversation]: The conversations.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    try:
        # Get the collection
        collection = await get_conversation_collection()

        # Get the conversations
        cursor = (
            collection.find({"userId": userId})
            .sort("updatedAt", -1)
            .skip(skip)
            .limit(limit)
        )

        # Initialize the conversations
        conversations = []

        # Iterate over the conversations
        async for doc in cursor:
            # Append the conversation
            conversations.append(Conversation(**doc))

        # Return the conversations
        return conversations

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error getting conversations by user: {str(e)}")

        # Re-raise the error
        raise


# Update the title of a conversation
async def update_conversation_title(conversationId: str, title: str) -> None:
    """
    Update the title of a conversation.

    Args:
        conversationId (str): The ID of the conversation.
        title (str): The new title.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    try:
        # Get the collection
        collection = await get_conversation_collection()

        # Update the conversation title
        await collection.update_one(
            {"_id": conversationId},
            {"$set": {"title": title, "updatedAt": datetime.utcnow()}},
        )

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error updating conversation title: {str(e)}")

        # Re-raise the error
        raise


# Export the functions
__all__ = [
    "get_conversation",
    "update_conversation_summary",
    "get_conversations_by_user",
    "update_conversation_title",
]
