# Standard Library Imports
import logging
from typing import Any, Dict, Optional

# Local Imports
from app.db.conversation import get_conversation, update_conversation_summary
from app.db.summary import generate_summary
from app.schemas import Conversation

# Set up logging
logger = logging.getLogger(__name__)


# Process a transcript
async def process_transcript(
    metadata: Dict[str, Any],
    transcript: Dict[str, Any],
    conversationId: Optional[str] = None,
) -> Conversation:
    """
    Process a transcript and create/update conversation and messages.

    Args:
        metadata (Dict[str, Any]): The metadata of the conversation.
        transcript (Dict[str, Any]): The transcript of the conversation.
        conversationId (Optional[str]): The ID of the conversation to resume.

    Returns:
        Conversation: The processed conversation.
    """

    try:
        # Get the user ID
        userId = metadata["user"]["userId"]

        # Get the agent ID
        agentId = metadata["agent"]["agentId"]

        # Get the conversation
        conversation = await get_conversation(conversationId, agentId, userId)

        # If conversation not found
        if not conversation:
            # Log the error
            logger.error(f"Conversation not found: {conversationId}")

            # Re-raise the error
            raise ValueError(f"Conversation not found: {conversationId}")

        # Get the transcript items ignoring the agent welcome message
        transcript_items = transcript["items"][2:]

        # Generate the summary
        new_summary = await generate_summary(conversation.summary, transcript_items)

        # Update the conversation summary
        await update_conversation_summary(conversation.id, new_summary)

        # Update the conversation object with the new summary
        conversation.summary = new_summary

        # Return the conversation
        return conversation

    except Exception as e:
        # Log the error
        logger.error(f"Error processing transcript: {str(e)}")

        # Re-raise the error
        raise


# Export the functions
__all__ = ["process_transcript"]
