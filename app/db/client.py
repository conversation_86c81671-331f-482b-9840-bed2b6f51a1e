# Standard Library Imports
import asyncio
import logging
from typing import Dict

# Third Party Imports
from pymongo import AsyncMongoClient, IndexModel
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# Local Imports
from app.core.config import settings

# Disable PyMongo logging
logging.getLogger("pymongo").setLevel(logging.WARNING)

# MongoDB client dictionary keyed by event loop ID
_clients: Dict[int, AsyncMongoClient] = {}
_dbs: Dict[int, object] = {}
_conversation_collections: Dict[int, object] = {}
_message_collections: Dict[int, object] = {}

# Initialize indexes
_conversation_indexes = [
    IndexModel([("userId", 1), ("agentId", 1)], background=True),
    IndexModel([("updatedAt", -1)], background=True),
]
_message_indexes = [
    IndexModel([("conversationId", 1), ("createdAt", -1)], background=True),
    IndexModel([("workflowId", 1)], background=True, sparse=True),
]


# Get event loop ID
def _get_loop_id() -> int:
    """
    Get the ID of the current event loop.

    Returns:
        int: The ID of the current event loop.
    """

    # Get the running loop
    loop = asyncio.get_running_loop()

    # Return the loop ID
    return id(loop)


# MongoDB client
async def get_client() -> AsyncMongoClient:
    """
    Get the MongoDB client instance.

    Returns:
        AsyncMongoClient: The MongoDB client.

    Raises:
        ConnectionFailure: If the connection to MongoDB fails.
    """

    # Get the current event loop ID
    loop_id = _get_loop_id()

    # Initialize the client if it doesn't exist for this loop
    global _clients

    # If client doesn't exist for this loop
    if loop_id not in _clients:
        try:
            # Create a new client with optimized settings
            client = AsyncMongoClient(
                settings.MONGODB_CONN_URI,
                maxPoolSize=10,  # Optimize connection pool size
                minPoolSize=1,
                maxIdleTimeMS=30000,  # Close idle connections after 30 seconds
                connectTimeoutMS=5000,  # 5 second connection timeout
                serverSelectionTimeoutMS=5000,  # 5 second server selection timeout
                retryWrites=True,  # Enable retryable writes
                w="majority",  # Write concern for data durability
            )

            # Test the connection
            await client.admin.command("ping")

            # Store the client
            _clients[loop_id] = client

        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            # Log the error
            logging.error(f"Failed to connect to MongoDB: {str(e)}")

            # Re-raise the error
            raise

    # Return the client
    return _clients[loop_id]


# Database
async def get_db():
    """
    Get the database instance.

    Returns:
        Database: The MongoDB database.
    """

    # Get the current event loop ID
    loop_id = _get_loop_id()

    # Initialize the database if it doesn't exist
    global _dbs

    # If database doesn't exist for this loop
    if loop_id not in _dbs:
        # Get the client
        client = await get_client()

        # Get the database
        _dbs[loop_id] = client[settings.MONGODB_DATABASE_NAME]

    # Return the database
    return _dbs[loop_id]


# Conversation Collection
async def get_conversation_collection():
    """
    Get the conversation collection.

    Returns:
        Collection: The conversation collection.
    """

    # Get the current event loop ID
    loop_id = _get_loop_id()

    # Initialize the collection if it doesn't exist
    global _conversation_collections

    # If collection doesn't exist for this loop
    if loop_id not in _conversation_collections:
        # Get the database
        db = await get_db()

        # Get the collection
        collection = db[settings.MONGODB_CONVERSATION_COLLECTION]

        # Create indexes if they don't exist
        await collection.create_indexes(_conversation_indexes)

        # Store the collection
        _conversation_collections[loop_id] = collection

    # Return the collection
    return _conversation_collections[loop_id]


# Message Collection
async def get_message_collection():
    """
    Get the message collection.

    Returns:
        Collection: The message collection.
    """

    # Get the current event loop ID
    loop_id = _get_loop_id()

    # Initialize the collection if it doesn't exist
    global _message_collections

    # If collection doesn't exist for this loop
    if loop_id not in _message_collections:
        # Get the database
        db = await get_db()

        # Get the collection
        collection = db[settings.MONGODB_MESSAGE_COLLECTION]

        # Create indexes if they don't exist
        await collection.create_indexes(_message_indexes)

        # Store the collection
        _message_collections[loop_id] = collection

    # Return the collection
    return _message_collections[loop_id]


# Close connections
async def close_connections():
    """
    Close all MongoDB connections.
    """

    # Get the current event loop ID
    loop_id = _get_loop_id()

    # Get the global variables
    global _clients, _dbs, _conversation_collections, _message_collections

    # If client exists for this loop
    if loop_id in _clients:
        # Close the client
        await _clients[loop_id].close()

        # Reset the global variables for this loop
        _clients.pop(loop_id, None)
        _dbs.pop(loop_id, None)
        _conversation_collections.pop(loop_id, None)
        _message_collections.pop(loop_id, None)


# Export the functions
__all__ = [
    "get_client",
    "get_db",
    "get_conversation_collection",
    "get_message_collection",
    "close_connections",
]
