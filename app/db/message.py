# Standard Library Imports
import logging
from typing import Any, Dict, List, Optional

# Third Party Imports
from pymongo.errors import PyMongoError

# Local Imports
from app.db.client import get_message_collection
from app.schemas import Message

# Set up logging
logger = logging.getLogger(__name__)


# Insert a message
async def insert_message(
    conversationId: str,
    role: str,
    content: Optional[str | List[str]] = None,
    workflowId: Optional[str] = None,
    workflowResponse: Optional[dict[str, Any]] = None,
) -> Message:
    """
    Insert a single message into the database.

    Args:
        conversationId (str): The ID of the conversation.
        role (str): The role of the sender ('user' or 'assistant').
        content (str | List[str]): The content of the message.
        workflowId (Optional[str], optional): The ID of the workflow. Defaults to None.
        workflowResponse (Optional[dict[str, Any]], optional): The workflow response. Defaults to None.

    Returns:
        Message: The inserted message.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    try:
        # Get the sender type
        senderType = "SENDER_TYPE_USER" if role == "user" else "SENDER_TYPE_ASSISTANT"

        # Process the content if it's a list
        processed_content = " ".join(content) if isinstance(content, list) else content

        # Create the message
        message = Message(
            conversationId=conversationId,
            senderType=senderType,
            content=processed_content,
            workflowId=workflowId,
            workflowResponse=workflowResponse,
        )

        # Get the collection
        collection = await get_message_collection()

        # Insert the message
        result = await collection.insert_one(message.dict(by_alias=True))

        # Set the ID of the message
        message.id = str(result.inserted_id)

        # Return the message
        return message

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error inserting message: {str(e)}")

        # Re-raise the error
        raise


# Get last messages
async def get_last_messages(conversationId: str, limit: int = 8) -> List[Message]:
    """
    Get the last messages of a conversation.

    Args:
        conversationId (str): The ID of the conversation.
        limit (int): The maximum number of messages to return.

    Returns:
        List[Message]: The last messages of the conversation.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    try:
        # Get the collection
        collection = await get_message_collection()

        # Get the last messages
        cursor = (
            collection.find({"conversationId": conversationId})
            .sort("createdAt", -1)
            .limit(limit)
        )

        # Initialize the messages
        messages = []

        # Iterate over the messages
        async for doc in cursor:
            # Append the message
            messages.append(Message(**doc))

        # Return the messages
        return messages

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error getting last messages: {str(e)}")

        # Re-raise the error
        raise


# Get messages by workflow
async def get_messages_by_workflow(workflowId: str) -> List[Message]:
    """
    Get messages by workflow ID.

    Args:
        workflowId (str): The ID of the workflow.

    Returns:
        List[Message]: The messages.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    try:
        # Get the collection
        collection = await get_message_collection()

        # Get the messages
        cursor = collection.find({"workflowId": workflowId}).sort("createdAt", 1)

        # Initialize the messages
        messages = []

        # Iterate over the messages
        async for doc in cursor:
            # Append the message
            messages.append(Message(**doc))

        # Return the messages
        return messages

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error getting messages by workflow: {str(e)}")

        # Re-raise the error
        raise


# Insert multiple messages in a batch
async def insert_messages_batch(messages: List[Dict[str, Any]]) -> List[str]:
    """
    Insert multiple messages in a batch operation.

    Args:
        messages (List[Dict[str, Any]]): The messages to insert.

    Returns:
        List[str]: The IDs of the inserted messages.

    Raises:
        PyMongoError: If there's an error with MongoDB.
    """

    # If no messages
    if not messages:
        # Return an empty list
        return []

    try:
        # Get the collection
        collection = await get_message_collection()

        # Insert the messages
        result = await collection.insert_many(messages)

        # Return the IDs
        return [str(id) for id in result.inserted_ids]

    except PyMongoError as e:
        # Log the error
        logger.error(f"Error inserting messages batch: {str(e)}")

        # Re-raise the error
        raise


# Export the functions
__all__ = [
    "insert_message",
    "get_last_messages",
    "get_messages_by_workflow",
    "insert_messages_batch",
]
