# Standard Library Imports
import json
import logging
import time
from typing import Any, Dict, List, Optional

# Local Imports
from app.kafka.agent_platform.message_processing import (
    extract_correlation_id,
    is_final_response,
    should_process_message,
)
from app.kafka.agent_platform.utils import json_loads


# Response listening methods
async def listen_for_responses(
    self, correlation_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Listen for responses from the Kafka topic with optional correlation ID filtering.

    Args:
        correlation_id: Optional correlation ID to filter messages.

    Returns:
        List of response messages matching the correlation ID.
    """

    # Ensure the client is initialized
    if not self._initialized:
        # Initialize the client
        await self.initialize()

    # Initialize response list
    responses: List[Dict[str, Any]] = []

    # Initialize timing and success flag
    start_time = time.time()
    success = False

    try:
        # Loop until manually stopped
        while True:
            try:
                # Get the next message
                msg = await self.consumer.getone()

                # Extract correlation ID from headers if present
                msg_correlation_id = extract_correlation_id(msg.headers)

                # Process the message if it matches our criteria
                if should_process_message(msg, msg_correlation_id, correlation_id):
                    try:
                        # Load the message as JSON
                        response = json_loads(msg.value)

                        # Append the response
                        responses.append(response)

                        # Check if we should stop listening
                        if is_final_response(response):
                            break

                    except (json.JSONDecodeError, ValueError, TypeError):
                        # Skip messages that aren't valid JSON
                        continue

            except Exception as e:
                # Log the warning
                logging.warning(f"Error processing Kafka message: {str(e)}")

                # Skip the message
                continue

        # Set success flag
        success = True

    except Exception as e:
        # Log the error
        logging.error(f"Error listening for Kafka responses: {str(e)}")

        # Re-raise the error
        raise

    finally:
        # Record metrics
        duration = time.time() - start_time

        # Record receive metrics
        self.metrics.record_receive(success, duration)

    # Return the responses
    return responses
