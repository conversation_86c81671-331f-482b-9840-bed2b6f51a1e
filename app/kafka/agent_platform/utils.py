# Standard Library Imports
import json
import logging
from typing import Any, Union

try:
    # Try to import orjson for faster JSON operations
    import orjson as json_lib

    # Define JSON serialization and deserialization functions
    def json_dumps(obj: Any) -> bytes:
        """
        Serialize an object to JSON bytes.

        Args:
            obj: The object to serialize.

        Returns:
            The serialized JSON bytes.
        """

        # Serialize the object to JSON bytes
        return json_lib.dumps(obj)

    # Deserialize JSON bytes to an object
    def json_loads(s: Union[str, bytes]) -> Any:
        """
        Deserialize JSON bytes to an object.

        Args:
            s: The JSON bytes to deserialize.

        Returns:
            The deserialized object.
        """

        # Deserialize the JSON bytes to an object
        return json_lib.loads(s)

except ImportError:
    try:
        # Try to import ujson for faster JSON operations
        import ujson as json_lib

        # Define JSON serialization and deserialization functions
        def json_dumps(obj: Any) -> bytes:
            """
            Serialize an object to JSON bytes.

            Args:
                obj: The object to serialize.

            Returns:
                The serialized JSON bytes.
            """

            # Serialize the object to JSON bytes
            return json_lib.dumps(obj).encode("utf-8")

        # Deserialize JSON bytes to an object
        def json_loads(s: Union[str, bytes]) -> Any:
            """
            Deserialize JSON bytes to an object.

            Args:
                s: The JSON bytes to deserialize.

            Returns:
                The deserialized object.
            """

            # Deserialize the JSON bytes to an object
            if isinstance(s, bytes):
                # Decode bytes to string
                s = s.decode("utf-8")

            # Return the deserialized object
            return json_lib.loads(s)

    except ImportError:
        # Fallback to standard json library
        json_lib = json

        # Define JSON serialization and deserialization functions
        def json_dumps(obj: Any) -> bytes:
            """
            Serialize an object to JSON bytes.

            Args:
                obj: The object to serialize.

            Returns:
                The serialized JSON bytes.
            """

            # Serialize the object to JSON bytes
            return json.dumps(obj).encode("utf-8")

        # Deserialize JSON bytes to an object
        def json_loads(s: Union[str, bytes]) -> Any:
            """
            Deserialize JSON bytes to an object.

            Args:
                s: The JSON bytes to deserialize.

            Returns:
                The deserialized object.
            """

            # Deserialize the JSON bytes to an object
            if isinstance(s, bytes):
                # Decode bytes to string
                s = s.decode("utf-8")

            # Return the deserialized object
            return json.loads(s)


# Configure logging for Kafka components
logging.getLogger("aiokafka").setLevel(logging.WARNING)
logging.getLogger("aiokafka.conn").setLevel(logging.WARNING)
logging.getLogger("aiokafka.consumer.group_coordinator").setLevel(logging.WARNING)
