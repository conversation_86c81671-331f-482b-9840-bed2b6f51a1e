# Standard Library Imports
import asyncio
import logging
import uuid
from typing import Any, Dict, List, Optional, Tuple

# Third Party Imports
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from aiokafka.errors import KafkaError

# Local Imports
from app.core.config import settings
from app.kafka.agent_platform.batch import (
    flush_batch,
    flush_batch_after_timeout,
    send_message,
    send_messages_batch,
)
from app.kafka.agent_platform.listener import listen_for_responses
from app.kafka.agent_platform.metrics import KafkaMetrics


# Kafka Test Client
class KafkaTestClient:
    """
    Kafka Test Client for communication with Kafka topics.

    This client provides methods to send messages to Kafka topics and
    listen for responses with correlation ID matching. It includes optimizations
    for performance, message batching, and connection health monitoring.
    """

    # Default batch settings
    DEFAULT_BATCH_SIZE = 10
    DEFAULT_BATCH_TIMEOUT = 0.5  # seconds

    # Connection health check settings
    HEALTH_CHECK_INTERVAL = 10.0  # seconds
    CONNECTION_TIMEOUT = 60.0  # seconds

    # Initialize the Kafka Test Client
    def __init__(self) -> None:
        """
        Initialize the Kafka Test Client with empty producer and consumer.

        Attributes:
            producer (Optional[AIOKafkaProducer]): The Kafka producer.
            consumer (Optional[AIOKafkaConsumer]): The Kafka consumer.
            _initialized (bool): Whether the client is initialized.
            _last_health_check (float): The timestamp of the last health check.
            _health_check_task (Optional[asyncio.Task]): The health check task.
        """

        # Initialize attributes
        self.producer: Optional[AIOKafkaProducer] = None
        self.consumer: Optional[AIOKafkaConsumer] = None
        self._initialized: bool = False
        self._last_health_check: float = 0
        self._health_check_task: Optional[asyncio.Task] = None

        # Message batching
        self._message_batch: Dict[
            str, List[Tuple[Dict[str, Any], Optional[List[Tuple[str, bytes]]]]]
        ] = {}
        self._batch_tasks: Dict[str, asyncio.Task] = {}

        # Performance metrics
        self.metrics = KafkaMetrics()

    #
    async def initialize(self) -> None:
        """
        Initialize the Kafka producer and consumer.

        This method sets up the Kafka producer and consumer with the appropriate
        configuration and starts them. It is idempotent and can be called multiple times.
        """

        # If already initialized
        if self._initialized:
            # Return early
            return

        try:
            # Create Kafka producer with optimized settings
            self.producer = AIOKafkaProducer(
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                max_request_size=524288000,  # 500MB max request size
                compression_type="gzip",  # Compression for better performance
                acks="all",  # Ensure durability
                linger_ms=10,  # Reduced for faster batching
                request_timeout_ms=5000,  # Reduced timeout for quicker failover
                retry_backoff_ms=20,  # Faster retry
            )

            # Create Kafka consumer with optimized settings
            self.consumer = AIOKafkaConsumer(
                settings.KAFKA_AGENT_RESPONSE_TOPIC,
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                group_id=f"test-group-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
                fetch_max_wait_ms=1000,  # Reduced for faster responsiveness
                fetch_max_bytes=10485760,  # 10MB max fetch size
                fetch_min_bytes=512,  # Reduced for quicker fetch
                max_partition_fetch_bytes=524288,  # 512KB per partition
                check_crcs=False,  # Skip CRC checks for performance
                session_timeout_ms=60000,  # Reduced for faster rebalance
                request_timeout_ms=50000,  # Reduced for quicker failover
            )

            # Start producer and consumer concurrently
            await asyncio.gather(self.producer.start(), self.consumer.start())

            # Set initialized flag
            self._initialized = True

        except KafkaError as e:
            # Log the error
            logging.error(f"Failed to initialize Kafka client: {str(e)}")

            # Cleanup resources
            await self._cleanup_resources()

            # Re-raise the exception
            raise

    # Cleanup resources
    async def cleanup(self) -> None:
        """
        Clean up Kafka resources by stopping the producer and consumer.

        This method safely stops the Kafka producer and consumer if they exist.
        """

        # Traverse the batch tasks
        for topic, task in self._batch_tasks.items():
            # If task is not done
            if not task.done():
                # Cancel the task
                task.cancel()

                try:
                    # Wait for the task to complete
                    await task

                except asyncio.CancelledError:
                    # Task was cancelled, exit cleanly
                    pass

        # If health check task is running
        if self._health_check_task and not self._health_check_task.done():
            # Cancel the health check task
            self._health_check_task.cancel()

            try:
                # Wait for the task to complete
                await self._health_check_task

            except asyncio.CancelledError:
                # Task was cancelled, exit cleanly
                pass

        # Clean up resources
        await self._cleanup_resources()

    # Cleanup resources
    async def _cleanup_resources(self) -> None:
        """
        Internal method to clean up Kafka resources.
        """

        # If producer exists
        if self.producer:
            try:
                # Stop the producer
                await self.producer.stop()

            except Exception as e:
                # Log the warning
                logging.warning(f"Error stopping Kafka producer: {str(e)}")

        # If consumer exists
        if self.consumer:
            try:
                # Stop the consumer
                await self.consumer.stop()

            except Exception as e:
                # Log the warning
                logging.warning(f"Error stopping Kafka consumer: {str(e)}")

        # Reset initialized flag
        self._initialized = False

        # Clear message batch and batch tasks
        self._message_batch.clear()

        # Clear batch tasks
        self._batch_tasks.clear()

    # Attach methods from other modules
    flush_batch_after_timeout = flush_batch_after_timeout
    flush_batch = flush_batch
    send_message = send_message
    send_messages_batch = send_messages_batch
    listen_for_responses = listen_for_responses
