# Standard Library Imports
from typing import Any, Dict, List, Optional, Tuple


# Message processing methods
def extract_correlation_id(
    headers: Optional[List[Tuple[bytes, bytes]]],
) -> Optional[str]:
    """
    Extract correlation ID from message headers.

    Args:
        headers: List of message headers as (key, value) tuples.

    Returns:
        Correlation ID as string if found, None otherwise.
    """

    # If no headers
    if not headers:
        # Return None
        return None

    # Convert headers to dictionary
    headers_dict = {
        k.decode("utf-8") if isinstance(k, bytes) else k: v.decode("utf-8")
        if isinstance(v, bytes)
        else v
        for k, v in headers
    }

    # Return the correlation ID
    return headers_dict.get("correlationId")


# Determine if a message should be processed
def should_process_message(
    msg: Any,
    msg_correlation_id: Optional[str],
    target_correlation_id: Optional[str],
) -> bool:
    """
    Determine if a message should be processed based on correlation ID.

    Args:
        msg: The Kafka message.
        msg_correlation_id: Correlation ID extracted from the message.
        target_correlation_id: Correlation ID we're looking for.

    Returns:
        True if the message should be processed, False otherwise.
    """
    # If no target correlation ID specified
    if not target_correlation_id:
        # Process all messages
        return True

    # Otherwise, only process messages with matching correlation ID
    return msg_correlation_id == target_correlation_id


# Determine if a response is the final one in a sequence
def is_final_response(response: Dict[str, Any]) -> bool:
    """
    Determine if a response is the final one in a sequence.

    Args:
        response: The response message.

    Returns:
        True if this is the final response, False otherwise.
    """

    # Check for agent response with final flag
    if "agent_response" in response and response.get("final", True):
        # Return True
        return True

    # Check for session ID (indicates completion of initialization)
    if "session_id" in response:
        # Return True
        return True

    # Return False
    return False
