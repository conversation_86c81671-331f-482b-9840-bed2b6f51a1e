# Standard Library Imports
import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Tuple

# Third Party Imports
from aiokafka.errors import KafkaError

# Local Imports
from app.kafka.agent_platform.utils import json_dumps


# Batch processing methods
async def flush_batch_after_timeout(self, topic: str) -> None:
    """
    Flush a batch after the timeout period.

    Args:
        topic: The Kafka topic to flush the batch for.
    """

    try:
        # Sleep for the timeout period
        await asyncio.sleep(self.DEFAULT_BATCH_TIMEOUT)

        # Flush the batch
        await self.flush_batch(topic)

    except asyncio.CancelledError:
        # Task was cancelled, exit cleanly
        pass

    except Exception as e:
        # Log the error
        logging.error(f"Error in batch flush task for topic {topic}: {str(e)}")


# Send message methods
async def flush_batch(self, topic: str) -> None:
    """
    Flush all batched messages for a topic.

    Args:
        topic: The Kafka topic to flush the batch for.
    """

    # Check if there's a batch to flush
    if topic not in self._message_batch or not self._message_batch[topic]:
        # No batch to flush
        return

    # Get the batch
    batch = self._message_batch[topic]

    # Clear the batch
    self._message_batch[topic] = []

    # If there's a batch task
    if topic in self._batch_tasks:
        # Remove the batch task
        self._batch_tasks.pop(topic, None)

    # Record start time
    start_time = time.time()

    # Set success flag
    success = False

    try:
        # Transform the batch into a list of messages
        for message, headers in batch:
            # Cover the message to JSON
            value = json_dumps(message)

            # Send the message
            await self.producer.send_and_wait(topic, value=value, headers=headers)

        # Set success flag
        success = True

    except KafkaError as e:
        # Log the error
        logging.error(f"Failed to send batch to topic {topic}: {str(e)}")

        # Re-raise the error
        raise

    finally:
        # Get the batch and duration
        batch_size = len(batch)
        duration = time.time() - start_time

        # Track metrics for each message in the batch
        for _ in range(batch_size):
            # Record metrics
            self.metrics.record_send(success, duration / batch_size)


# Send message methods
async def send_message(
    self,
    topic: str,
    message: Dict[str, Any],
    headers: Optional[List[Tuple[str, bytes]]] = None,
    batch: bool = False,
) -> None:
    """
    Send a message to the specified Kafka topic.

    Args:
        topic: The Kafka topic to send the message to.
        message: The message payload as a dictionary.
        headers: Optional list of (key, value) tuples to include as message headers.
        batch: Whether to batch this message with others to the same topic.

    Raises:
        KafkaError: If there's an error sending the message.
    """

    # Ensure the client is initialized
    if not self._initialized:
        # Initialize the client
        await self.initialize()

    # If batching is enabled
    if batch:
        # If there's no batch for this topic
        if topic not in self._message_batch:
            # Create a new batch
            self._message_batch[topic] = []

            # Start a new batch task for this topic
            self._batch_tasks[topic] = asyncio.create_task(
                self.flush_batch_after_timeout(topic)
            )

        # Add the message to the batch
        self._message_batch[topic].append((message, headers))

        # If batch size threshold reached
        if len(self._message_batch[topic]) >= self.DEFAULT_BATCH_SIZE:
            # Flush the batch
            await self.flush_batch(topic)

        # Return
        return

    # Record start time
    start_time = time.time()

    # Set success flag
    success = False

    try:
        # Convert the message to JSON
        value = json_dumps(message)

        # Send the message
        await self.producer.send_and_wait(topic, value=value, headers=headers)

        # Set success flag
        success = True

    except KafkaError as e:
        # Log the error
        logging.error(f"Failed to send message to topic {topic}: {str(e)}")

        # Re-raise the error
        raise

    finally:
        # Record metrics
        self.metrics.record_send(success, time.time() - start_time)


# Send batch of messages
async def send_messages_batch(
    self,
    topic: str,
    messages: List[Dict[str, Any]],
    headers_list: Optional[List[Optional[List[Tuple[str, bytes]]]]] = None,
) -> None:
    """
    Send multiple messages to the same topic in a batch.

    Args:
        topic: The Kafka topic to send the messages to.
        messages: List of message payloads as dictionaries.
        headers_list: Optional list of headers for each message.

    Raises:
        KafkaError: If there's an error sending the batch.
    """

    # If not initialized
    if not self._initialized:
        # Initialize the client
        await self.initialize()

    # If no messages
    if not messages:
        # Return
        return

    # If no headers list
    if headers_list is None:
        # Create a list of None headers
        headers_list = [None] * len(messages)

    # Record start time
    start_time = time.time()

    # Set success flag
    success = False

    try:
        # Transform the batch into a list of messages
        for i, message in enumerate(messages):
            # Get the headers
            headers = headers_list[i] if i < len(headers_list) else None

            # Convert the message to JSON
            value = json_dumps(message)

            # Send the message
            await self.producer.send_and_wait(topic, value=value, headers=headers)

        # Set success flag
        success = True

    except KafkaError as e:
        # Log the error
        logging.error(f"Failed to send batch to topic {topic}: {str(e)}")

        # Re-raise the error
        raise

    finally:
        # Get the batch and duration
        batch_size = len(messages)
        duration = time.time() - start_time

        # Traverse the batch and record metrics
        for _ in range(batch_size):
            # Record metrics
            self.metrics.record_send(success, duration / batch_size)
