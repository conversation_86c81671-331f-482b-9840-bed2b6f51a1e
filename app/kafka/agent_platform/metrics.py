# Standard Library Imports
from collections import deque
from typing import Any, Deque, Dict


# Kafka Metrics
class KafkaMetrics:
    """Simple metrics collector for Kafka operations."""

    # Initialize the metrics
    def __init__(self) -> None:
        """
        Initialize the Kafka metrics.

        Attributes:
            messages_sent (int): The number of messages sent.
            messages_received (int): The number of messages received.
            send_errors (int): The number of send errors.
            receive_errors (int): The number of receive errors.
            send_times (Deque[float]): The send times.
            receive_times (Deque[float]): The receive times.
        """

        # Initialize the metrics
        self.messages_sent: int = 0
        self.messages_received: int = 0
        self.send_errors: int = 0
        self.receive_errors: int = 0
        self.send_times: Deque[float] = deque(maxlen=100)  # Store last 100 send times
        self.receive_times: Deque[float] = deque(
            maxlen=100
        )  # Store last 100 receive times

    # Record a message send operation
    def record_send(self, success: bool, duration: float) -> None:
        """
        Record a message send operation.

        Args:
            success (bool): Whether the send was successful.
            duration (float): The duration of the send operation.
        """

        # If successful
        if success:
            # Increment the message count
            self.messages_sent += 1

            # Store the send time
            self.send_times.append(duration)

        else:
            # Increment the error count
            self.send_errors += 1

    # Record a message receive operation
    def record_receive(self, success: bool, duration: float) -> None:
        """
        Record a message receive operation.

        Args:
            success (bool): Whether the receive was successful.
            duration (float): The duration of the receive operation.
        """

        # If successful
        if success:
            # Increment the message count
            self.messages_received += 1

            # Store the receive time
            self.receive_times.append(duration)

        else:
            # Increment the error count
            self.receive_errors += 1

    # Get the average send time
    def get_avg_send_time(self) -> float:
        """
        Get the average send time in milliseconds.

        Returns:
            The average send time in milliseconds.
        """

        # If no send times
        if not self.send_times:
            # Return 0
            return 0.0

        # Return the average send time
        return sum(self.send_times) / len(self.send_times) * 1000

    # Get the average receive time
    def get_avg_receive_time(self) -> float:
        """
        Get the average receive time in milliseconds.

        Returns:
            The average receive time in milliseconds.
        """

        # If no receive times
        if not self.receive_times:
            # Return 0
            return 0.0

        # Return the average receive time
        return sum(self.receive_times) / len(self.receive_times) * 1000

    # Get all metrics
    def get_stats(self) -> Dict[str, Any]:
        """
        Get all metrics as a dictionary.

        Returns:
            A dictionary of metrics.
        """

        # Return the metrics
        return {
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "send_errors": self.send_errors,
            "receive_errors": self.receive_errors,
            "avg_send_time_ms": self.get_avg_send_time(),
            "avg_receive_time_ms": self.get_avg_receive_time(),
        }
