# Standard Library Imports
import asyncio
import logging
import time
import uuid


# Local Imports
async def background_consumer_task(self) -> None:
    """
    Background task that continuously consumes messages from Kafka.

    This task processes incoming messages and routes them to the appropriate
    queue based on their correlation ID.
    """

    # Retry parameters
    retry_count = 0
    max_retries = 5
    backoff_time = 1  # Initial backoff time in seconds

    # Topic subscription
    topic_subscription = self.consumer.subscription()

    # If the consumer has no topic subscription
    if not topic_subscription:
        # Log the error
        logging.error("Consumer has no topic subscription! Check configuration.")

        # Return
        return

    # Loop while initialized
    while self._initialized:
        try:
            # Process messages from the consumer
            async for msg in self.consumer:
                # Verify message value is not None
                if msg.value is None:
                    # Log the warning
                    logging.warning(
                        f"Received message with None value from topic: {msg.topic}"
                    )

                    # Continue
                    continue

                # Extract correlation ID from headers
                correlation_id = self._extract_correlation_id(msg.headers)

                # If correlation ID exists
                if correlation_id:
                    # Set start time and success flag
                    start_time = time.time()
                    success = False

                    try:
                        # Process the message
                        await self._process_message(correlation_id, msg.value)

                        # Set success flag
                        success = True

                    except Exception as e:
                        # Log the error
                        logging.error(f"Error processing message: {str(e)}")

                    finally:
                        # Get queue size
                        queue_size = self._get_queue_size(correlation_id)

                        # Record metrics
                        self.metrics.record_processing(
                            success, time.time() - start_time, queue_size
                        )
                else:
                    # Log missing correlation ID
                    logging.warning("Received message without correlation ID, skipping")

            # Reset retry parameters on successful iteration
            retry_count = 0
            backoff_time = 1

            # Short sleep to prevent CPU spinning if consumer returns immediately
            await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            # Task was cancelled, exit cleanly
            break

        except Exception as e:
            # If service is not initialized
            if not self._initialized:
                break

            # Increment retry count
            retry_count += 1

            # If max retries reached
            if retry_count > max_retries:
                # Log the error
                logging.error(
                    f"Too many errors in Kafka consumer task, giving up after {max_retries} retries: {str(e)}"
                )

                # Break out of the loop
                break

            # Use exponential backoff with jitter
            jitter = (
                0.5 + 0.5 * uuid.uuid4().int / 2**64
            )  # Random value between 0.5 and 1

            # Calculate wait time
            wait_time = backoff_time * jitter

            # Log the warning
            logging.warning(
                f"Error in Kafka consumer task (retry {retry_count}/{max_retries}), "
                f"retrying in {wait_time:.2f}s: {str(e)}"
            )

            # Sleep for the calculated time
            await asyncio.sleep(wait_time)

            # Double the backoff time
            backoff_time = min(backoff_time * 2, 60)

            try:
                # If consumer exists and has subscription
                if (
                    hasattr(self, "consumer")
                    and hasattr(self.consumer, "subscription")
                    and self.consumer.subscription()
                ):
                    # Stop and start the consumer
                    await self.consumer.stop()
                    await self.consumer.start()

            except Exception as restart_error:
                # Log the error
                logging.error(f"Failed to restart Kafka consumer: {str(restart_error)}")
