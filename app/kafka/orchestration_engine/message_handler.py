# Standard Library Imports
import asyncio
import logging
import time
from typing import Optional

# Local Imports
from app.kafka.orchestration_engine.utils import json_dumps


# Message handling methods
async def get_message(
    self, correlation_id: str, timeout: float = 30.0
) -> Optional[str]:
    """
    Get a single message for the specified correlation ID.

    This method waits for a message with the given correlation ID to be
    available, or until the timeout expires.

    Args:
        correlation_id: The correlation ID to get a message for.
        timeout: Maximum time in seconds to wait for a message.

    Returns:
        The message as a JSON string, or None if timeout occurs.
    """

    # Ensure the service is initialized
    await self._ensure_initialized()

    # Check if consumer service is properly initialized
    if not self._initialized:
        # Log the error
        logging.error("Consumer service is not properly initialized")

        # Return None
        return None

    # Get or create queue for this correlation ID
    queue = self._get_or_create_queue(correlation_id)

    # Update last access time
    self._active_correlation_ids[correlation_id] = time.time()

    # Record queue operation
    self.metrics.record_queue_operation()

    # Set start time and success flag
    start_time = time.time()
    success = False

    try:
        # Wait for a message with timeout
        message = await asyncio.wait_for(queue.get(), timeout=timeout)

        # Mark task as done
        queue.task_done()

        # Format the message as JSON
        result = json_dumps(message).decode("utf-8")

        # Set success flag
        success = True

        # Return the message
        return result

    except asyncio.TimeoutError:
        # Return None on timeout
        return None

    except Exception as e:
        # Log the error
        logging.error(
            f"Error getting message for correlation ID {correlation_id}: {str(e)}"
        )

        # Return None on error
        return None

    finally:
        # Get queue size
        queue_size = self._get_queue_size(correlation_id)

        # Record metrics
        self.metrics.record_processing(success, time.time() - start_time, queue_size)
