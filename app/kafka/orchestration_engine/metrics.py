# Standard Library Imports
from collections import deque
from typing import Any, Deque, Dict


# Kafka Metrics
class KafkaMetrics:
    """Simple metrics collector for Kafka operations."""

    def __init__(self) -> None:
        """
        Initialize the Kafka metrics.

        Attributes:
            messages_processed (int): The number of messages processed.
            processing_errors (int): The number of processing errors.
            queue_operations (int): The number of queue operations.
            processing_times (Deque[float]): The processing times.
            queue_sizes (Deque[int]): The queue sizes.
        """

        # Initialize the metrics
        self.messages_processed: int = 0
        self.processing_errors: int = 0
        self.queue_operations: int = 0
        self.processing_times: Deque[float] = deque(
            maxlen=100
        )  # Store last 100 processing times
        self.queue_sizes: Deque[int] = deque(
            maxlen=100
        )  # Store last 100 queue size samples

    # Record a message processing operation
    def record_processing(
        self, success: bool, duration: float, queue_size: int
    ) -> None:
        """
        Record a message processing operation.

        Args:
            success (bool): Whether the processing was successful.
            duration (float): The duration of the processing operation.
            queue_size (int): The size of the queue.
        """

        # If successful
        if success:
            # Increment the message count
            self.messages_processed += 1

            # Store the processing time
            self.processing_times.append(duration)

        else:
            # Increment the error count
            self.processing_errors += 1

        # Store the queue size
        self.queue_sizes.append(queue_size)

    # Record a queue operation
    def record_queue_operation(self) -> None:
        """Record a queue operation."""

        # Increment the queue operation count
        self.queue_operations += 1

    # Get the average processing time
    def get_avg_processing_time(self) -> float:
        """
        Get the average processing time in milliseconds.

        Returns:
            The average processing time in milliseconds.
        """

        # If no processing times
        if not self.processing_times:
            # Return 0
            return 0.0

        # Return the average processing time
        return sum(self.processing_times) / len(self.processing_times) * 1000

    # Get the average queue size
    def get_avg_queue_size(self) -> float:
        """
        Get the average queue size.

        Returns:
            The average queue size.
        """

        # If no queue sizes
        if not self.queue_sizes:
            # Return 0
            return 0.0

        # Return the average queue size
        return sum(self.queue_sizes) / len(self.queue_sizes)

    # Get all metrics
    def get_stats(self) -> Dict[str, Any]:
        """
        Get all metrics as a dictionary.

        Returns:
            A dictionary of metrics.
        """

        # Return the metrics
        return {
            "messages_processed": self.messages_processed,
            "processing_errors": self.processing_errors,
            "queue_operations": self.queue_operations,
            "avg_processing_time_ms": self.get_avg_processing_time(),
            "avg_queue_size": self.get_avg_queue_size(),
        }
