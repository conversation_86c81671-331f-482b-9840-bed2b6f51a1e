# Standard Library Imports
import asyncio
import logging
import time


# Cleanup methods
async def cleanup_stale_resources_task(self) -> None:
    """
    Background task that periodically cleans up stale resources.

    This task removes queues and correlation IDs that haven't been
    accessed for a while to prevent memory leaks.
    """

    try:
        # Loop while initialized
        while self._initialized:
            # Sleep first to allow the service to initialize fully
            await asyncio.sleep(self.CLEANUP_INTERVAL)

            # Clean up stale resources
            await self._cleanup_stale_resources()

    except asyncio.CancelledError:
        # Task was cancelled, exit cleanly
        pass

    except Exception as e:
        # Log the error
        logging.error(f"Error in cleanup task: {str(e)}")

        # If service is still initialized
        if self._initialized:
            # Restart the task
            self._cleanup_task = asyncio.create_task(
                self._cleanup_stale_resources_task()
            )


# Cleanup stale resources
async def cleanup_stale_resources(self) -> None:
    """
    Clean up stale resources to prevent memory leaks.

    This method removes queues and correlation IDs that haven't been
    accessed for a while.
    """

    # Get current time
    now = time.time()

    # Initialize list of stale IDs
    stale_ids = []

    # Find stale correlation IDs
    for correlation_id, last_access in self._active_correlation_ids.items():
        # If last access time is older than TTL
        if now - last_access > self.QUEUE_TTL:
            # Add to stale IDs
            stale_ids.append(correlation_id)

    # Remove stale resources
    for correlation_id in stale_ids:
        # Remove from active correlation IDs
        self._active_correlation_ids.pop(correlation_id, None)

        # Remove from response queues
        self._response_queues.pop(correlation_id, None)

    # Log if any stale resources were cleaned up
    if stale_ids:
        # Log the cleanup
        logging.info(f"Cleaned up {len(stale_ids)} stale resources")


# Cleanup resources
async def cleanup_resources(self) -> None:
    """
    Clean up resources used by the Kafka consumer service.

    This method clears the response queues and active correlation IDs.
    """

    # Clear response queues
    self._response_queues.clear()

    # Clear active correlation IDs
    self._active_correlation_ids.clear()


# Cleanup Kafka resources
async def cleanup_kafka_resources(self) -> None:
    """
    Clean up Kafka resources.

    This method stops the consumer and cancels the background tasks.
    """

    # Stop the consumer if it exists
    if hasattr(self, "consumer") and self.consumer:
        try:
            # Stop the consumer
            await self.consumer.stop()

        except Exception as e:
            # Log the warning
            logging.warning(f"Error stopping Kafka consumer: {str(e)}")

    # If consumer task is running
    if self._consumer_task and not self._consumer_task.done():
        # Cancel the consumer task
        self._consumer_task.cancel()

        try:
            # Wait for the task to complete
            await self._consumer_task

        except asyncio.CancelledError:
            # Task was cancelled, exit cleanly
            pass

        except Exception as e:
            # Log the warning
            logging.warning(f"Error cancelling consumer task: {str(e)}")

    # If cleanup task is running
    if self._cleanup_task and not self._cleanup_task.done():
        # Cancel the cleanup task
        self._cleanup_task.cancel()

        try:
            # Wait for the task to complete
            await self._cleanup_task

        except asyncio.CancelledError:
            # Task was cancelled, exit cleanly
            pass

        except Exception as e:
            # Log the warning
            logging.warning(f"Error cancelling cleanup task: {str(e)}")

    # Reset initialization flag
    self._initialized = False
