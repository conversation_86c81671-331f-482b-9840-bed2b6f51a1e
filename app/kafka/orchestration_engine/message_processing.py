# Standard Library Imports
import asyncio
import logging
import time
from typing import Any, Optional


# Message processing methods
async def process_message(self, correlation_id: str, message: Any) -> None:
    """
    Process a message by adding it to the appropriate queue.

    Args:
        correlation_id: The correlation ID for the message.
        message: The message payload.
    """
    # Validate message
    if message is None:
        logging.warning(f"Received None message for correlation ID: {correlation_id}")
        return

    # Get or create queue for this correlation ID
    queue = self._get_or_create_queue(correlation_id)

    # Check if queue is full
    queue_size = queue.qsize()
    if queue_size >= self.MAX_QUEUE_SIZE:
        try:
            # Get one item without waiting
            _ = queue.get_nowait()

            # Mark as done
            queue.task_done()

            # Log the eviction
            logging.warning(
                f"Queue for {correlation_id} is full, evicting oldest message"
            )

        except asyncio.QueueEmpty:
            # Queue was emptied by another consumer
            pass

    # Add the message to the queue
    await queue.put(message)

    # Update last access time for this correlation ID
    self._active_correlation_ids[correlation_id] = time.time()

    # Record queue operation
    self.metrics.record_queue_operation()


# Extract correlation ID from message headers
def extract_correlation_id(self, headers: Optional[list]) -> Optional[str]:
    """
    Extract correlation ID from message headers.

    Args:
        headers: List of message headers as (key, value) tuples.

    Returns:
        Correlation ID as string if found, None otherwise.
    """

    # If no headers
    if not headers:
        logging.warning("Message has no headers, cannot extract correlation ID")
        return None

    # Convert headers to dictionary
    headers_dict = {}

    # Loop through headers
    for key, value in headers:
        try:
            # Decode key and value if necessary
            k = key.decode("utf-8") if isinstance(key, bytes) else key
            v = value.decode("utf-8") if isinstance(value, bytes) else value

            # Add to dictionary
            headers_dict[k] = v

        except Exception as e:
            logging.warning(f"Error decoding header {key}: {str(e)}")
            continue

    # Check if correlationId exists
    correlation_id = headers_dict.get("correlationId")

    # Try alternative header formats if correlation ID not found
    if not correlation_id:
        for header_key in headers_dict:
            if header_key.lower() == "correlationid":
                correlation_id = headers_dict[header_key]
                break

    # Return the correlation ID
    return correlation_id


# Get or create a queue for a correlation ID
def get_or_create_queue(self, correlation_id: str) -> asyncio.Queue:
    """
    Get an existing queue or create a new one for the correlation ID.

    Args:
        correlation_id: The correlation ID to get or create a queue for.

    Returns:
        The queue for the correlation ID.
    """

    # If queue doesn't exist
    if correlation_id not in self._response_queues:
        # Create a new queue
        self._response_queues[correlation_id] = asyncio.Queue(
            maxsize=self.MAX_QUEUE_SIZE
        )

        # Initialize last access time
        self._active_correlation_ids[correlation_id] = time.time()

    # Return the queue
    return self._response_queues[correlation_id]


# Get the size of a queue
def get_queue_size(self, correlation_id: str) -> int:
    """
    Get the current size of a queue.

    Args:
        correlation_id: The correlation ID of the queue.

    Returns:
        The current size of the queue, or 0 if it doesn't exist.
    """

    # If queue exists
    if correlation_id in self._response_queues:
        # Return the queue size
        return self._response_queues[correlation_id].qsize()

    # Return 0 if queue doesn't exist
    return 0
