# Standard Library Imports
import asyncio
import logging
import uuid
from typing import Any, Dict

# Third Party Imports
from aiokafka import AIOKafkaConsumer
from aiokafka.errors import KafkaError

# Local Imports
from app.core.config import settings
from app.kafka.orchestration_engine.cleanup import (
    cleanup_kafka_resources,
    cleanup_stale_resources,
    cleanup_stale_resources_task,
)
from app.kafka.orchestration_engine.consumer import background_consumer_task
from app.kafka.orchestration_engine.message_handler import get_message
from app.kafka.orchestration_engine.message_processing import (
    extract_correlation_id,
    get_or_create_queue,
    get_queue_size,
    process_message,
)
from app.kafka.orchestration_engine.metrics import KafkaMetrics
from app.kafka.orchestration_engine.utils import json_loads


# Kafka Consumer Service
class KafkaConsumerService:
    """
    Singleton class for consuming Kafka messages.

    This service provides a background consumer that processes messages
    from Kafka topics and makes them available through queues based on
    correlation IDs. It includes optimizations for performance, memory
    management, and error handling.
    """

    # Singleton instance and state flags
    _instance = None
    _initialized: bool = False
    _consumer_task = None

    # Queue configuration
    MAX_QUEUE_SIZE = 1000  # Maximum number of messages per queue
    QUEUE_TTL = 300  # Time-to-live for queues in seconds
    CLEANUP_INTERVAL = 60  # Interval for cleaning up stale queues in seconds

    # Connection health check settings
    HEALTH_CHECK_INTERVAL = 60.0  # seconds

    # Store response queues as instance variable instead of global
    _response_queues: Dict[str, asyncio.Queue] = {}

    # Track active correlation IDs for cleanup with last access time
    _active_correlation_ids: Dict[str, float] = {}

    # Cleanup task
    _cleanup_task = None

    def __new__(cls):
        """
        Implement the singleton pattern to ensure only one instance exists.

        Returns:
            KafkaConsumerService: The singleton instance.
        """

        # If the instance does not exist
        if cls._instance is None:
            # Instantiate the class
            cls._instance = super(KafkaConsumerService, cls).__new__(cls)

            # Dict for storing response queues
            cls._instance._response_queues = {}

            # Dict for tracking active correlation IDs
            cls._instance._active_correlation_ids = {}

            # Initialize the consumer and producer
            cls._instance.metrics = KafkaMetrics()

        # Return the singleton instance
        return cls._instance

    # Initialize the Kafka consumer service
    async def initialize(self) -> None:
        """
        Initialize the Kafka consumer service.

        This method sets up the Kafka consumer with optimized settings and
        starts the background consumer and cleanup tasks. It is idempotent and
        thread-safe, minimizing startup time by parallelizing task creation.
        """

        # If no init lock exists
        if not hasattr(self, "init_lock"):
            # Create a new lock
            self.init_lock = asyncio.Lock()

        # If already initialized
        async with self.init_lock:
            # If already initialized
            if self._initialized:
                return

            try:
                # Create Kafka consumer with optimized settings
                self.consumer = AIOKafkaConsumer(
                    settings.KAFKA_WORKFLOW_RESPONSES,
                    bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                    group_id=f"workflow-responses-{str(uuid.uuid4())}",  # Unique group ID
                    auto_offset_reset="latest",
                    enable_auto_commit=True,
                    value_deserializer=lambda m: json_loads(m),
                    fetch_max_wait_ms=200,  # Reduced for faster responsiveness
                    fetch_max_bytes=52428800,  # 50MB max fetch size
                    fetch_min_bytes=512,  # Reduced for quicker fetch
                    max_partition_fetch_bytes=1048576,  # 1MB per partition
                    check_crcs=False,  # Skip CRC checks for performance
                    session_timeout_ms=6000,  # Adjusted to avoid InvalidSessionTimeoutError
                    request_timeout_ms=7000,  # Increased to handle network delays
                )

                # Start the Kafka consumer
                await self.consumer.start()

                # Set initialized flag first to prevent consumer task from exiting immediately
                self._initialized = True

                # Start the background consumer task without awaiting completion
                if not self._consumer_task or self._consumer_task.done():
                    # Create the task
                    self._consumer_task = asyncio.create_task(
                        self._background_consumer_task()
                    )

                    # Add error handling callback for the task
                    self._consumer_task.add_done_callback(
                        self._handle_consumer_task_done
                    )

                # Start the cleanup task without awaiting completion
                if not self._cleanup_task or self._cleanup_task.done():
                    # Create the task
                    self._cleanup_task = asyncio.create_task(
                        self._cleanup_stale_resources_task()
                    )

                    # Add error handling callback for the task
                    self._cleanup_task.add_done_callback(self._handle_cleanup_task_done)

            # Handle any Kafka errors
            except KafkaError as e:
                # Log the error
                logging.error(f"Failed to initialize Kafka consumer: {str(e)}")

                # Cleanup resources
                await self._cleanup_resources()

                # Re-raise the exception
                raise

    # Handle consumer task completion callback
    def _handle_consumer_task_done(self, task):
        """
        Handle consumer task completion callback.
        """
        try:
            # Get the result to check for exceptions
            exception = task.exception()

            # If an exception occurred
            if exception:
                # Log the error
                logging.error(f"Consumer task failed with exception: {exception}")

                # Reset task reference so it can be restarted
                self._consumer_task = None

                # Try to restart the task if service is still initialized
                if self._initialized:
                    # Create the task
                    self._consumer_task = asyncio.create_task(
                        self._background_consumer_task()
                    )

                    # Add the done callback
                    self._consumer_task.add_done_callback(
                        self._handle_consumer_task_done
                    )

        # If the task was cancelled
        except asyncio.CancelledError:
            # Task was cancelled, no need to log
            pass

        # If an exception occurred
        except Exception as e:
            # Log the error
            logging.error(f"Error handling consumer task completion: {str(e)}")

    # Handle cleanup task completion callback
    def _handle_cleanup_task_done(self, task):
        """
        Handle cleanup task completion callback.
        """

        try:
            # Get the result to check for exceptions
            exception = task.exception()

            # If an exception occurred
            if exception:
                # Log the error
                logging.error(f"Cleanup task failed with exception: {exception}")

                # Reset task reference so it can be restarted
                self._cleanup_task = None

                # Try to restart the task if service is still initialized
                if self._initialized:
                    # Create the task
                    self._cleanup_task = asyncio.create_task(
                        self._cleanup_stale_resources_task()
                    )

                    # Add the done callback
                    self._cleanup_task.add_done_callback(self._handle_cleanup_task_done)

        except asyncio.CancelledError:
            # Task was cancelled, no need to log
            pass

        # If an exception occurred
        except Exception as e:
            # Log the error
            logging.error(f"Error handling cleanup task completion: {str(e)}")

    # Stop the Kafka consumer service
    async def _ensure_initialized(self) -> None:
        """
        Ensure that the Kafka consumer service is initialized.
        """

        # If not initialized
        if not self._initialized:
            # Initialize the service
            await self.initialize()

    # Stop all Kafka services
    async def stop_services(self) -> None:
        """
        Stop all Kafka services.
        """

        # If the service is not initialized
        if not self._initialized:
            # Return early
            return

        # Stop the consumer & cleanup tasks
        await self._cleanup_resources()

    # Get the current metrics
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get the current metrics for the Kafka consumer service.
        """

        # Get metrics
        metrics = self.metrics.get_stats()

        # Add custom metrics
        metrics.update(
            {
                "active_correlation_ids": len(self._active_correlation_ids),
                "response_queues": len(self._response_queues),
                "consumer_task_running": self._consumer_task is not None
                and not self._consumer_task.done(),
                "cleanup_task_running": self._cleanup_task is not None
                and not self._cleanup_task.done(),
            }
        )

        # Return the metrics
        return metrics

    # Attach methods from other modules
    _background_consumer_task = background_consumer_task
    _cleanup_stale_resources_task = cleanup_stale_resources_task
    _cleanup_stale_resources = cleanup_stale_resources
    _cleanup_resources = cleanup_kafka_resources
    _extract_correlation_id = extract_correlation_id
    _process_message = process_message
    _get_or_create_queue = get_or_create_queue
    _get_queue_size = get_queue_size
    get_message = get_message
