# Standard Library Imports
import json
import logging

# Third Party Imports
from livekit import agents
from livekit.plugins import silero

# Local Imports
from app.db.client import close_connections

# Local Imports
from app.services.conversation import get_agent_instructions, write_transcript
from app.services.metrics import (
    create_usage_collector,
    log_usage,
)
from app.services.session import create_agent_session, start_agent

# Set root logger to WARNING (or ERROR)
logging.basicConfig(level=logging.WARNING)

# Specifically silence aiokafka debug logs
logging.getLogger("aiokafka").setLevel(logging.WARNING)
logging.getLogger("aiokafka.conn").setLevel(logging.WARNING)
logging.getLogger("aiokafka.consumer.group_coordinator").setLevel(logging.WARNING)


# Set the logger for this module
def prewarm(proc: agents.JobProcess):
    """
    Prewarm the process by loading the VAD model.

    Args:
        proc (agents.JobProcess): The process to prewarm.
    """

    # Load the VAD model
    proc.userdata["vad"] = silero.VAD.load()


# Entrypoint for the agent
async def entrypoint(ctx: agents.JobContext):
    """
    Entrypoint for the agent.

    Args:
        ctx (agents.JobContext): The context for the agent.
    """

    # Set log context fields for better debugging
    ctx.log_context_fields = {
        "room": ctx.room.name,
    }

    # Initialize the usage collector
    usage_collector = create_usage_collector()

    # Define the usage logging callback
    async def _log_usage_callback():
        """
        Log the usage of the agent.
        """

        # Log the usage
        await log_usage(usage_collector)

    # Define MongoDB cleanup callback
    async def _mongodb_cleanup_callback():
        """
        Clean up MongoDB connections.
        """

        try:
            # Close MongoDB connections
            await close_connections()

        except Exception as e:
            # Log the error but don't raise
            ctx.log.error(f"Failed to clean up MongoDB connections: {str(e)}")

    # Add the shutdown callbacks
    ctx.add_shutdown_callback(_log_usage_callback)
    ctx.add_shutdown_callback(_mongodb_cleanup_callback)

    # Connect to the room
    await ctx.connect()

    # Extract the room metadata
    room_metadata = json.loads(ctx.room.metadata)

    # Initialize the session
    session = create_agent_session(ctx, usage_collector)

    # Define the transcript writing callback
    async def _write_transcript_callback():
        """
        Write the transcript of the agent to a file.
        """

        # Process the transcript
        await write_transcript(ctx, session)

    # Add the transcript writing callback
    ctx.add_shutdown_callback(_write_transcript_callback)

    # Get the agent instructions
    instructions = await get_agent_instructions(room_metadata)

    # Wait for a participant to join the room
    await ctx.wait_for_participant()

    # Start the agent
    assistant = await start_agent(
        session=session,
        room=ctx.room,
        instructions=instructions,
        room_metadata=room_metadata,
    )

    # Send user a message
    await ctx.room.local_participant.send_text(
        json.dumps(
            {
                "is_initial_response": True,
                "content": "Agnet Platform Ready!",
                "error": False,
            }
        ),
        topic="lk.transcription",
    )

    # Define the assistant cleanup callback with proper error handling
    async def _assistant_cleanup_callback():
        """
        Clean up the assistant resources with proper error handling.
        """

        try:
            # Cleanup the assistant
            await assistant.cleanup()

        except Exception as e:
            # Log the error but don't raise
            ctx.log.error(f"Error during assistant cleanup: {str(e)}")

    # Add the assistant cleanup callback
    ctx.add_shutdown_callback(_assistant_cleanup_callback)


# Export the entrypoint and prewarm functions
__all__ = ["entrypoint", "prewarm"]
