# Third Party Imports
from pydantic_settings import BaseSettings


# Settings Class
class Settings(BaseSettings):
    """
    Settings class.

    Attributes:
        APP_NAME (str): The name of the application.
        LIVEKIT_URL (str): The URL of the LiveKit server.
        LIVEKIT_API_KEY (str): The API key for the LiveKit server.
        LIVEKIT_API_SECRET (str): The API secret for the LiveKit server.
        DEEPGRAM_API_KEY (str): The API key for the Deepgram service.
        OPENAI_API_KEY (str): The API key for the OpenAI service.
        KAFKA_BOOTSTRAP_SERVERS (str): The Kafka bootstrap servers.
        KAFKA_AGENT_CREATION_TOPIC (str): The Kafka topic for agent creation requests.
        KAFKA_AGENT_CHAT_TOPIC (str): The Kafka topic for agent chat requests.
        KAFKA_AGENT_RESPONSE_TOPIC (str): The Kafka topic for agent chat responses.
        KAFKA_CONSUMER_GROUP (str): The Kafka consumer group.
    """

    # Service Configuration
    APP_NAME: str = "livekit-backend"

    # LiveKit Configuration
    LIVEKIT_URL: str = "http://localhost:7881"
    LIVEKIT_API_KEY: str = "lk_livekit"
    LIVEKIT_API_SECRET: str = "lk_secret"

    # Deepgram Configuration
    DEEPGRAM_API_KEY: str = "deepgram-api-key"

    # OpenAI Configuration
    OPENAI_API_KEY: str = "openai-api-key"

    # Summary Gen Configuration
    SUMMARY_GEN_API_KEY: str = "gemini-api-key"
    SUMMARY_GEN_MODEL: str = "gemini-2.0-flash"

    # MongoDB Configuration
    MONGODB_CONN_URI: str = "mongodb://localhost:27017"
    MONGODB_DATABASE_NAME: str = "communication_db"
    MONGODB_CONVERSATION_COLLECTION: str = "conversation"
    MONGODB_MESSAGE_COLLECTION: str = "message"

    # Kafka Configuration
    KAFKA_BOOTSTRAP_SERVERS: str = "kafka.ruh.ai:9094"
    KAFKA_AGENT_CREATION_TOPIC: str = "agent_creation_requests"
    KAFKA_AGENT_CHAT_TOPIC: str = "agent_chat_requests"
    KAFKA_AGENT_RESPONSE_TOPIC: str = "agent_chat_responses"
    KAFKA_CONSUMER_GROUP: str = "autogen-agent-service-group"
    KAFKA_WORKFLOW_RESPONSES: str = "workflow-responses"

    # API Gateway Configuration
    API_GATEWAY_BASE_URL: str = "http://localhost:8000"

    # Class Configuration
    class Config:
        """
        Configuration class.

        Attributes:
            env_file (str): The environment file.
            case_sensitive (bool): Whether the configuration is case sensitive.
        """

        # Environment file
        env_file = ".env"

        # Case sensitive
        case_sensitive = True


# Initialize settings
settings = Settings()

# Export the settings
__all__ = ["settings"]
