# Standard Library Imports
from datetime import datetime
from enum import Enum
from typing import Optional

# Third Party Imports
from bson import ObjectId
from pydantic import BaseModel, Field


# Channel Type
class ChannelType(str, Enum):
    """Channel type.

    Attributes:
        CHANNEL_TYPE_WEB (str): Web channel type.
    """

    # Web
    CHANNEL_TYPE_WEB = "CHANNEL_TYPE_WEB"


# Chat Type
class ChatType(str, Enum):
    """Chat type.

    Attributes:
        CHAT_TYPE_SINGLE (str): Single chat type.
        CHAT_TYPE_MULTI (str): Multi chat type.
    """

    # Single
    CHAT_TYPE_SINGLE = "CHAT_TYPE_SINGLE"

    # Multi
    CHAT_TYPE_MULTI = "CHAT_TYPE_MULTI"


# Conversation class
class Conversation(BaseModel):
    """Conversation.

    Attributes:
        id (Optional[str]): The ID of the conversation.
        userId (str): The ID of the user.
        agentId (str): The ID of the agent.
        title (str): The title of the conversation.
        channel (ChannelType): The channel type of the conversation.
        chatType (ChatType): The chat type of the conversation.
        summary (str): The summary of the conversation.
        createdAt (datetime): The creation time of the conversation.
        updatedAt (datetime): The update time of the conversation.
    """

    # ID
    id: Optional[str] = Field(default_factory=lambda: str(ObjectId()), alias="_id")

    # User ID
    userId: str

    # Agent ID
    agentId: str

    # Title
    title: str

    # Channel
    channel: ChannelType

    # Chat Type
    chatType: ChatType = Field(default=ChatType.CHAT_TYPE_SINGLE)

    # Summary
    summary: Optional[str] = ""

    # Created At
    createdAt: datetime = Field(default_factory=datetime.utcnow)

    # Updated At
    updatedAt: datetime = Field(default_factory=datetime.utcnow)

    # Config
    class Config:
        """
        Config.
        Attributes:
            json_encoders (dict): The JSON encoders.
            allow_population_by_field_name (bool): Whether to allow population by field name.
        """

        # JSON Encoders
        json_encoders = {
            datetime: lambda dt: dt.isoformat(),
            ObjectId: lambda oid: str(oid),
        }

        # Allow population by field name
        allow_population_by_field_name = True

    # Constructor
    def __init__(self, **data):
        # If both 'id' and '_id' are provided
        if "id" in data and "_id" not in data:
            # Set '_id' to 'id'
            data["_id"] = data["id"]

        # Call the parent constructor
        super().__init__(**data)


# Export the Conversation class and enums
__all__ = ["Conversation", "ChannelType", "ChatType"]
