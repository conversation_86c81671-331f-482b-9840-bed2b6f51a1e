# Standard Library Imports
from datetime import datetime
from enum import Enum
from typing import Any, Optional

# Third Party Imports
from bson import ObjectId
from pydantic import BaseModel, Field


# Sender Type
class SenderType(str, Enum):
    """
    Sender type.

    Attributes:
        SENDER_TYPE_USER (str): User sender type.
        SENDER_TYPE_ASSISTANT (str): Assistant sender type.
    """

    # User
    SENDER_TYPE_USER = "SENDER_TYPE_USER"

    # Assistant
    SENDER_TYPE_ASSISTANT = "SENDER_TYPE_ASSISTANT"


# Message
class Message(BaseModel):
    """
    Message.

    Attributes:
        id (Optional[str]): The ID of the message.
        conversationId (str): The ID of the conversation this message belongs to.
        senderType (SenderType): The type of the sender.
        content (str): The content of the message.
        workflowId (Optional[str]): The ID of the workflow associated with this message.
        workflowResponse (Optional[dict[str, Any]]): The response from the workflow.
        createdAt (datetime): The creation time of the message.
        updatedAt (datetime): The update time of the message.
    """

    # ID
    id: Optional[str] = Field(default_factory=lambda: str(ObjectId()), alias="_id")

    # Conversation ID
    conversationId: str

    # Sender Type
    senderType: SenderType

    # Content (optional)
    content: Optional[str] = None

    # Workflow ID (optional)
    workflowId: Optional[str] = None

    # Worflow Response (optional)
    workflowResponse: Optional[dict[str, Any]] = None

    # Created At
    createdAt: datetime = Field(default_factory=datetime.utcnow)

    # Updated At
    updatedAt: datetime = Field(default_factory=datetime.utcnow)

    # Config
    class Config:
        """
        Config.

        Attributes:
            json_encoders (dict): The JSON encoders.
            allow_population_by_field_name (bool): Whether to allow population by field name.
        """

        # JSON Encoders
        json_encoders = {
            datetime: lambda dt: dt.isoformat(),
            ObjectId: lambda oid: str(oid),
        }

        # Allow population by field name
        allow_population_by_field_name = True


# Export the Message class and enums
__all__ = ["Message", "SenderType"]
