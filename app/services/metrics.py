# Third Party Imports
from livekit.agents import AgentSession, metrics
from livekit.agents.voice import MetricsCollectedEvent


# Create a usage collector
def create_usage_collector() -> metrics.UsageCollector:
    """
    Create a usage collector.

    Returns:
        metrics.UsageCollector: The usage collector.
    """

    # Initialize the usage collector
    return metrics.UsageCollector()


# Log the usage
async def log_usage(usage_collector: metrics.UsageCollector) -> None:
    """
    Log the usage of the agent.

    Args:
        usage_collector (metrics.UsageCollector): The usage collector.
    """

    # Get the usage summary
    summary = usage_collector.get_summary()

    # Log the usage
    print(f"\n\nUsage: {summary}\n\n", flush=True)


# Set up metrics collection for a session
def setup_metrics_collection(
    session: AgentSession, usage_collector: metrics.UsageCollector
) -> None:
    """
    Set up metrics collection for a session.

    Args:
        session (AgentSession): The agent session.
        usage_collector (metrics.UsageCollector): The usage collector.
    """

    # Collect the metrics
    @session.on("metrics_collected")
    def _on_metrics_collected(event: MetricsCollectedEvent):
        """
        Collect the metrics of the agent.

        Args:
            event (MetricsCollectedEvent): The metrics event.
        """

        # Collect the metrics for summary
        usage_collector.collect(event.metrics)


# Export the functions
__all__ = [
    "create_usage_collector",
    "log_usage",
    "setup_metrics_collection",
]
