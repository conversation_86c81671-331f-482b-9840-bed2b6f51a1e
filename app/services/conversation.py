# Standard Library Imports
import json
from typing import Any, Dict

# Third Party Imports
from livekit.agents import AgentSession, JobContext

# Local Imports
from app.db import (
    get_conversation,
    get_last_messages,
    process_transcript,
)


# Process the transcript
async def write_transcript(ctx: JobContext, session: AgentSession) -> None:
    """
    Write the transcript of the agent to a file.

    Args:
        ctx (JobContext): The context for the agent.
        session (AgentSession): The agent session.
    """

    # Get the session transcript
    transcript = session.history.to_dict()

    # If no items in transcript
    if len(session.history.items) < 4:
        # Return
        return

    # Process the transcript
    await process_transcript(
        metadata=json.loads(ctx.room.metadata),
        transcript=transcript,
        conversationId=json.loads(ctx.room.metadata)
        .get("conversation", None)
        .get("conversationId", None),
    )


# Get the agent instructions
async def get_agent_instructions(room_metadata: Dict[str, Any]) -> str:
    """
    Get the agent instructions.

    Args:
        room_metadata (Dict[str, Any]): The room metadata.

    Returns:
        str: The agent instructions.
    """

    # Get the conversation Id
    conversationId = room_metadata["conversation"]["conversationId"]

    # Get the agent Id
    agentId = room_metadata["agent"]["agentId"]

    # Get the user Id
    userId = room_metadata["user"]["userId"]

    # Get the conversation
    conversation = await get_conversation(conversationId, agentId, userId)

    # If the conversation found
    if conversation:
        # Get last messages
        conversation_last_messages = await get_last_messages(conversationId, 8)

        # Prepare the messages
        conversation_last_messages_str = "\n".join(
            [f"{m.senderType}: {m.content}" for m in conversation_last_messages]
        )

        # Set the instructions for the agent
        instructions = f"""You are a helpful voice assistant.
You should be careful to not suggest anything that would be dangerous, illegal or inappropriate.
You can remember past interactions and use them to inform your answers.
Use semantic memory retrieval to provide contextually relevant responses.

Conversation Summary:
{conversation.summary}

Conversation Last Messages:
{conversation_last_messages_str}"""

    else:
        # Raise an error if conversation not found
        raise ValueError(f"Conversation with ID {conversationId} not found")

    # Return the instructions
    return instructions


# Export the functions
__all__ = ["write_transcript", "get_agent_instructions"]
