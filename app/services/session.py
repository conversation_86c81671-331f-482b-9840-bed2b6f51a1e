# Standard Library Imports
import asyncio
from typing import Any, Dict

# Third Party Imports
from livekit.agents import (
    AgentSession,
    AudioConfig,
    BackgroundAudioPlayer,
    BuiltinAudioClip,
    ChatContext,
    JobContext,
    RoomOutputOptions,
    metrics,
)
from livekit.plugins import deepgram, openai
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.rtc import Room

# Local Imports
from app.assistant import Assistant
from app.services.metrics import setup_metrics_collection


# Create an agent session
def create_agent_session(
    ctx: JobContext, usage_collector: metrics.UsageCollector
) -> AgentSession:
    """
    Create an agent session.

    Args:
        ctx (agents.JobContext): The context for the agent.
        usage_collector (metrics.UsageCollector): The usage collector.

    Returns:
        AgentSession: The agent session.
    """

    # Initialize the session
    session = AgentSession(
        stt=deepgram.STT(model="nova-3", language="multi"),
        llm=openai.LLM(model="gpt-4o-mini"),
        tts=openai.TTS(
            model="gpt-4o-mini-tts",
            voice="ash",
            speed=1,
        ),
        vad=ctx.proc.userdata["vad"],
        turn_detection=MultilingualModel(),
    )

    # Set up metrics collection
    setup_metrics_collection(session, usage_collector)

    # Return the session
    return session


# Start the agent
async def start_agent(
    session: AgentSession,
    room: Room,
    instructions: str,
    room_metadata: Dict[str, Any],
) -> Assistant:
    """
    Start the agent.

    Args:
        session (AgentSession): The agent session.
        room (Room): The room.
        instructions (str): The instructions for the agent.
        room_metadata (Dict[str, Any]): The room metadata.

    Returns:
        Assistant: The initialized assistant instance.
    """

    # Set the initial context for the agent
    initial_ctx = ChatContext()
    initial_ctx.add_message(role="assistant", content=instructions)

    # Create and initialize the assistant
    assistant = Assistant(
        room=room,
        instructions=instructions,
        chat_ctx=initial_ctx,
        user_id=room_metadata.get("user", None).get("userId", None),
        agent_id=room_metadata.get("agent", None).get("agentId", None),
        run_id=room_metadata.get("conversation", None).get("conversationId", None),
    )

    # Initialize the assistant
    await assistant.initialize()

    # Start the agent
    await session.start(
        room=room,
        agent=assistant,
        room_output_options=RoomOutputOptions(),
    )

    # Initialize background_audio to None to ensure proper cleanup
    background_audio = None

    # Use try/except to handle potential background audio issues
    try:
        # Set up the background audio with a timeout to prevent blocking
        background_audio = BackgroundAudioPlayer(
            ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.8),
            thinking_sound=[
                AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING, volume=0.8),
                AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING2, volume=0.7),
            ],
        )

        # Add background audio to assistant for proper cleanup
        assistant.background_audio = background_audio

        # Start the background audio with a timeout
        start_task = asyncio.create_task(
            background_audio.start(room=room, agent_session=session)
        )

        try:
            # Wait for the background audio to start (with a timeout)
            await asyncio.wait_for(start_task, timeout=5.0)

        # If the background audio does not start
        except asyncio.TimeoutError:
            # Cancel the task
            start_task.cancel()

            try:
                # Wait for the task to complete
                await start_task

            except (asyncio.CancelledError, Exception):
                # Log but continue
                pass

    except Exception:
        # Log the error but continue without background audio
        assistant.background_audio = None

    # Return the assistant instance
    return assistant


# Export the functions
__all__ = ["create_agent_session", "start_agent"]
