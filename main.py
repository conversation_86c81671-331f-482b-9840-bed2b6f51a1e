# Standard Library Imports
import logging
import warnings

# Ignore / Suppress All Warnings
warnings.filterwarnings("ignore")

# Third Party Imports
from dotenv import load_dotenv
from livekit import agents

# Local Imports
from app.entrypoint import entrypoint, prewarm

# Load environment variables from .env file
load_dotenv()


# Set root logger to WARNING (or ERROR)
logging.basicConfig(level=logging.WARNING)

# Specifically silence aiokafka debug logs
logging.getLogger("aiokafka").setLevel(logging.WARNING)
logging.getLogger("aiokafka.conn").setLevel(logging.WARNING)
logging.getLogger("aiokafka.consumer.group_coordinator").setLevel(logging.WARNING)


# Entrypoint Function
if __name__ == "__main__":
    # Run the LiveKit Agent
    agents.cli.run_app(
        agents.WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
            initialize_process_timeout=30,
        )
    )
