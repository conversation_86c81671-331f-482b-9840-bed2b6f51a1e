from _typeshed import Incomplete
from typing import ClassVar, final

default_pool: DescriptorPool

@final
class Arena: ...

@final
class Descriptor:
    containing_type: Incomplete
    enum_types: Incomplete
    enum_types_by_name: Incomplete
    enum_values_by_name: Incomplete
    extension_ranges: Incomplete
    extensions: Incomplete
    extensions_by_name: Incomplete
    fields: Incomplete
    fields_by_camelcase_name: Incomplete
    fields_by_name: Incomplete
    fields_by_number: Incomplete
    file: Incomplete
    full_name: Incomplete
    has_options: Incomplete
    is_extendable: Incomplete
    name: Incomplete
    nested_types: Incomplete
    nested_types_by_name: Incomplete
    oneofs: Incomplete
    oneofs_by_name: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def CopyToProto(self, object, /): ...
    def EnumValueName(self, *args, **kwargs): ...  # incomplete
    def GetOptions(self): ...

@final
class DescriptorPool:
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def Add(self, object, /): ...
    def AddSerializedFile(self, object, /): ...
    def FindAllExtensions(self, object, /): ...
    def FindEnumTypeByName(self, object, /): ...
    def FindExtensionByName(self, object, /): ...
    def FindExtensionByNumber(self, *args, **kwargs): ...  # incomplete
    def FindFieldByName(self, object, /): ...
    def FindFileByName(self, object, /): ...
    def FindFileContainingSymbol(self, object, /): ...
    def FindMessageTypeByName(self, object, /): ...
    def FindMethodByName(self, object, /): ...
    def FindOneofByName(self, object, /): ...
    def FindServiceByName(self, object, /): ...
    def SetFeatureSetDefaults(self, object, /): ...

@final
class EnumDescriptor:
    containing_type: Incomplete
    file: Incomplete
    full_name: Incomplete
    has_options: Incomplete
    is_closed: Incomplete
    name: Incomplete
    values: Incomplete
    values_by_name: Incomplete
    values_by_number: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def CopyToProto(self, object, /): ...
    def GetOptions(self): ...

@final
class EnumValueDescriptor:
    has_options: Incomplete
    index: Incomplete
    name: Incomplete
    number: Incomplete
    type: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def GetOptions(self): ...

@final
class ExtensionDict:
    def __contains__(self, other) -> bool: ...
    def __delitem__(self, other) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __ge__(self, other: object) -> bool: ...
    def __getitem__(self, index): ...
    def __gt__(self, other: object) -> bool: ...
    def __iter__(self): ...
    def __le__(self, other: object) -> bool: ...
    def __len__(self) -> int: ...
    def __lt__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __setitem__(self, index, object) -> None: ...

@final
class ExtensionIterator:
    def __iter__(self): ...
    def __next__(self): ...

@final
class FieldDescriptor:
    CPPTYPE_BOOL: ClassVar[int] = ...
    CPPTYPE_BYTES: ClassVar[int] = ...
    CPPTYPE_DOUBLE: ClassVar[int] = ...
    CPPTYPE_ENUM: ClassVar[int] = ...
    CPPTYPE_FLOAT: ClassVar[int] = ...
    CPPTYPE_INT32: ClassVar[int] = ...
    CPPTYPE_INT64: ClassVar[int] = ...
    CPPTYPE_MESSAGE: ClassVar[int] = ...
    CPPTYPE_STRING: ClassVar[int] = ...
    CPPTYPE_UINT32: ClassVar[int] = ...
    CPPTYPE_UINT64: ClassVar[int] = ...
    LABEL_OPTIONAL: ClassVar[int] = ...
    LABEL_REPEATED: ClassVar[int] = ...
    LABEL_REQUIRED: ClassVar[int] = ...
    TYPE_BOOL: ClassVar[int] = ...
    TYPE_BYTES: ClassVar[int] = ...
    TYPE_DOUBLE: ClassVar[int] = ...
    TYPE_ENUM: ClassVar[int] = ...
    TYPE_FIXED32: ClassVar[int] = ...
    TYPE_FIXED64: ClassVar[int] = ...
    TYPE_FLOAT: ClassVar[int] = ...
    TYPE_GROUP: ClassVar[int] = ...
    TYPE_INT32: ClassVar[int] = ...
    TYPE_INT64: ClassVar[int] = ...
    TYPE_MESSAGE: ClassVar[int] = ...
    TYPE_SFIXED32: ClassVar[int] = ...
    TYPE_SFIXED64: ClassVar[int] = ...
    TYPE_SINT32: ClassVar[int] = ...
    TYPE_SINT64: ClassVar[int] = ...
    TYPE_STRING: ClassVar[int] = ...
    TYPE_UINT32: ClassVar[int] = ...
    TYPE_UINT64: ClassVar[int] = ...
    camelcase_name: Incomplete
    containing_oneof: Incomplete
    containing_type: Incomplete
    cpp_type: Incomplete
    default_value: Incomplete
    enum_type: Incomplete
    extension_scope: Incomplete
    file: Incomplete
    full_name: Incomplete
    has_default_value: Incomplete
    has_options: Incomplete
    has_presence: Incomplete
    index: Incomplete
    is_extension: Incomplete
    is_packed: Incomplete
    json_name: Incomplete
    label: Incomplete
    message_type: Incomplete
    name: Incomplete
    number: Incomplete
    type: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def GetOptions(self): ...

@final
class FileDescriptor:
    dependencies: Incomplete
    enum_types_by_name: Incomplete
    extensions_by_name: Incomplete
    has_options: Incomplete
    message_types_by_name: Incomplete
    name: Incomplete
    package: Incomplete
    pool: Incomplete
    public_dependencies: Incomplete
    serialized_pb: Incomplete
    services_by_name: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def CopyToProto(self, object, /): ...
    def GetOptions(self): ...

@final
class MapIterator:
    def __iter__(self): ...
    def __next__(self): ...

@final
class Message:
    Extensions: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete  # incomplete
    def ByteSize(self): ...
    def Clear(self): ...
    def ClearExtension(self, object, /): ...
    def ClearField(self, object, /): ...
    def CopyFrom(self, object, /): ...
    def DiscardUnknownFields(self): ...
    def FindInitializationErrors(self): ...
    @classmethod
    def FromString(cls, object, /): ...
    def HasExtension(self, object, /): ...
    def HasField(self, object, /): ...
    def IsInitialized(self, *args, **kwargs): ...  # incomplete
    def ListFields(self): ...
    def MergeFrom(self, object, /): ...
    def MergeFromString(self, object, /): ...
    def ParseFromString(self, object, /): ...
    def SerializePartialToString(self, *args, **kwargs): ...  # incomplete
    def SerializeToString(self, *args, **kwargs): ...  # incomplete
    def SetInParent(self): ...
    def UnknownFields(self): ...
    def WhichOneof(self, object, /): ...
    def __contains__(self, other) -> bool: ...
    def __deepcopy__(self, memo=None): ...
    def __delattr__(self, name): ...
    def __eq__(self, other: object) -> bool: ...
    def __ge__(self, other: object) -> bool: ...
    def __gt__(self, other: object) -> bool: ...
    def __le__(self, other: object) -> bool: ...
    def __lt__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __setattr__(self, name, value): ...

@final
class MessageMeta(type): ...

@final
class MethodDescriptor:
    client_streaming: Incomplete
    containing_service: Incomplete
    full_name: Incomplete
    index: Incomplete
    input_type: Incomplete
    name: Incomplete
    output_type: Incomplete
    server_streaming: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def CopyToProto(self, object, /): ...
    def GetOptions(self): ...

@final
class OneofDescriptor:
    containing_type: Incomplete
    fields: Incomplete
    full_name: Incomplete
    has_options: Incomplete
    index: Incomplete
    name: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def GetOptions(self): ...

@final
class RepeatedCompositeContainer:
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def MergeFrom(self, object, /): ...
    def add(self, *args, **kwargs): ...  # incomplete
    def append(self, object, /): ...
    def extend(self, object, /): ...
    def insert(self, *args, **kwargs): ...  # incomplete
    def pop(self, *args, **kwargs): ...  # incomplete
    def remove(self, object, /): ...
    def reverse(self): ...
    def sort(self, *args, **kwargs): ...  # incomplete
    def __deepcopy__(self, memo=None): ...
    def __delitem__(self, other) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __ge__(self, other: object) -> bool: ...
    def __getitem__(self, index): ...
    def __gt__(self, other: object) -> bool: ...
    def __le__(self, other: object) -> bool: ...
    def __len__(self) -> int: ...
    def __lt__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __setitem__(self, index, object) -> None: ...

@final
class RepeatedScalarContainer:
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def MergeFrom(self, object, /): ...
    def append(self, object, /): ...
    def extend(self, object, /): ...
    def insert(self, *args, **kwargs): ...  # incomplete
    def pop(self, *args, **kwargs): ...  # incomplete
    def remove(self, object, /): ...
    def reverse(self): ...
    def sort(self, *args, **kwargs): ...  # incomplete
    def __deepcopy__(self, memo=None): ...
    def __delitem__(self, other) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __ge__(self, other: object) -> bool: ...
    def __getitem__(self, index): ...
    def __gt__(self, other: object) -> bool: ...
    def __le__(self, other: object) -> bool: ...
    def __len__(self) -> int: ...
    def __lt__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __reduce__(self): ...
    def __setitem__(self, index, object) -> None: ...

@final
class ServiceDescriptor:
    file: Incomplete
    full_name: Incomplete
    index: Incomplete
    methods: Incomplete
    methods_by_name: Incomplete
    name: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def CopyToProto(self, object, /): ...
    def FindMethodByName(self, object, /): ...
    def GetOptions(self): ...

@final
class UnknownFieldSet:
    def __init__(self, *args, **kwargs) -> None: ...  # incomplete
    def __getitem__(self, index): ...
    def __len__(self) -> int: ...

def SetAllowOversizeProtos(object, /): ...  # incomplete
