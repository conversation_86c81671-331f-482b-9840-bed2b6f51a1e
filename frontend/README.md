# LiveKit Voice Communication Frontend

A simple HTML/JavaScript frontend to test your simplified LiveKit voice communication backend.

## Features

- 🎤 **Voice Input** - Enable microphone to send voice to the agent
- 🔊 **Voice Output** - Receive and play audio responses from the agent
- 💬 **Message Display** - See transcribed messages and responses
- 🔗 **Real-time Connection** - Connect to LiveKit rooms in real-time

## Quick Start

### 1. Generate a LiveKit Token

First, generate a token for testing:

```bash
# Make sure you're in the frontend directory
cd frontend

# Install livekit-api if not already installed
pip install livekit-api

# Generate a token
python3 generate_token.py
```

The script will ask for:
- **API Key** (default: `devkey` - for local development)
- **API Secret** (default: `secret` - for local development)
- **Room Name** (default: `test-room`)
- **Participant Name** (default: `test-user`)
- **Token TTL** (default: 24 hours)

### 2. Start Your Backend

Make sure your LiveKit backend is running:

```bash
# In your main project directory
./run_local.sh
```

### 3. Open the Frontend

Simply open `index.html` in your web browser:

```bash
# Option 1: Open directly
open frontend/index.html

# Option 2: Serve with Python (recommended)
cd frontend
python3 -m http.server 8080
# Then open http://localhost:8080 in your browser
```

### 4. Test the Connection

1. **Enter Connection Details:**
   - LiveKit URL: `ws://localhost:7880` (default for local development)
   - Token: Paste the token generated in step 1
   - Room Name: `test-room` (or whatever you used when generating the token)

2. **Connect to Room:**
   - Click "Connect to Room"
   - Wait for "Connected to room" status

3. **Enable Voice:**
   - Click "🎤 Enable Microphone" to start sending voice
   - Click "🔊 Enable Speaker" to hear responses
   - Grant microphone permissions when prompted by your browser

4. **Test Voice Communication:**
   - Speak into your microphone
   - Your voice will be sent to the agent platform via Kafka
   - You should receive audio responses back through the LiveKit room

## Configuration

### LiveKit Server Settings

For local development, the default LiveKit server settings are:
- URL: `ws://localhost:7880`
- API Key: `devkey`
- API Secret: `secret`

### Room Metadata

The backend expects room metadata in this format:
```json
{
  "user": {
    "userId": "test-user"
  },
  "agent": {
    "agentId": "test-agent"
  },
  "conversation": {
    "conversationId": "test-conversation"
  }
}
```

## Troubleshooting

### Connection Issues

1. **"Connection failed" error:**
   - Make sure your LiveKit backend is running
   - Check that the LiveKit URL is correct
   - Verify your token is valid and not expired

2. **"Token invalid" error:**
   - Generate a new token with the correct API key/secret
   - Make sure the room name matches

3. **No audio:**
   - Check browser permissions for microphone
   - Make sure speaker is enabled
   - Check browser console for errors

### Browser Compatibility

This frontend works best with modern browsers that support:
- WebRTC
- Web Audio API
- ES6+ JavaScript

Tested browsers:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### HTTPS Requirements

For production use, you'll need HTTPS for:
- Microphone access
- Secure WebSocket connections
- Some WebRTC features

## Voice Communication Flow

1. **User speaks** → Browser captures audio via microphone
2. **Audio sent to LiveKit** → Real-time audio streaming
3. **Backend processes** → Deepgram STT → Kafka → Agent Platform
4. **Agent responds** → Kafka → OpenAI TTS → LiveKit
5. **User hears response** → Audio played through browser

## Development

### Customizing the Frontend

The frontend is a single HTML file with embedded CSS and JavaScript. You can customize:

- **Styling**: Modify the CSS in the `<style>` section
- **Behavior**: Update the JavaScript functions
- **UI Elements**: Add or remove HTML elements

### Adding Features

Some ideas for enhancements:
- Recording/playback of conversations
- Visual indicators for voice activity
- Chat text input as fallback
- Connection quality indicators
- Multiple room support

## Security Notes

⚠️ **Important for Production:**

1. **Never expose API secrets** in frontend code
2. **Generate tokens server-side** in production
3. **Use HTTPS** for all connections
4. **Implement proper authentication**
5. **Validate all user inputs**

This frontend is designed for testing and development. For production use, implement proper security measures and token generation on your backend.
