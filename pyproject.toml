[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "livekit-backend"
version = "0.1.0"
description = "LiveKit backend service"
authors = ["Rohit Ingole <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.10"
python-dotenv = "1.1.0"
livekit = "1.0.6"
livekit-api = "1.0.2"
livekit-agents = { version = "1.0.17", extras = ["cartesia", "deepgram", "elevenlabs", "google", "openai", "silero", "turn-detector"] }
pymongo = "4.12.0"
pydantic = "2.11.3"
pydantic-settings = "2.9.1"
autogen-agentchat = "0.5.5"
autogen-core = "0.5.5"
autogen-ext = { version = "0.5.5", extras = ["gemini", "openai"] }
aiokafka = "0.12.0"
orjson = "3.10.18"
ujson = "5.10.0"
tensorflow = "2.19.0"

[tool.poetry.group.dev.dependencies]
black = "25.1.0"
flake8 = "7.2.0"
mypy-extensions = "1.1.0"
pycodestyle = "2.13.0"
pyflakes = "3.3.2"
types-protobuf = "4.25.0.20240417"
ruff = "0.11.7"

[tool.poetry.group.test.dependencies]
pytest = "8.3.5"
pytest-asyncio = "0.26.0"
pytest-cov = "6.1.1"
pytest-mock = "3.14.0"
coverage = "7.8.0"

[tool.poetry.scripts]
livekit-backend = "main:main"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
exclude = '''
/(
    \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "--cov=app --cov-report=term-missing --cov-report=xml:coverage.xml"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = ["tests/*", "**/__pycache__/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError"
]

[tool.ruff]
line-length = 88
exclude = [
    ".git",
    ".venv",
    "__pycache__",
    ".hg",
    ".mypy_cache",
    ".tox",
    "_build",
    "buck-out",
    "build",
    "dist"
]
ignore = ["E402"]